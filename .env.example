# AI Data Platform Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
ENVIRONMENT=development
SECRET_KEY=your-super-secret-key-change-in-production
DOMAIN=localhost
APP_NAME=AI Data Platform

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ai_platform
POSTGRES_PASSWORD=postgres
POSTGRES_USER=postgres
POSTGRES_DB=ai_platform

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Authentication & Security
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256
MFA_ISSUER=AI Data Platform

# Object Storage (MinIO/S3)
MINIO_ENDPOINT=localhost:9000
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123
MINIO_BUCKET_NAME=ai-platform-data
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin123

# ML & AI Services
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# MLflow Configuration
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_BACKEND_STORE_URI=postgresql://postgres:postgres@localhost:5432/ai_platform
MLFLOW_DEFAULT_ARTIFACT_ROOT=s3://mlflow-artifacts

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=changeme

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
FROM_EMAIL=<EMAIL>

# Monitoring & Logging
LOG_LEVEL=INFO
SENTRY_DSN=your-sentry-dsn
PROMETHEUS_URL=http://localhost:9090

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=AI Data Platform
NEXT_PUBLIC_ENVIRONMENT=development

# Cloud Provider Credentials (for multi-cloud ML)
# AWS
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=************

# Azure
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=ai-platform-rg
AZURE_TENANT_ID=your-tenant-id
AZURE_CLIENT_ID=your-client-id
AZURE_CLIENT_SECRET=your-client-secret

# Google Cloud
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Compliance & Security
GDPR_ENABLED=true
SOC2_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years
DATA_RETENTION_DAYS=365

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload Limits
MAX_UPLOAD_SIZE=*********  # 100MB in bytes
ALLOWED_FILE_TYPES=csv,json,xlsx,parquet,txt

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=ai-platform-backups

# SSL/TLS Configuration
SSL_ENABLED=false
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Development Settings
DEBUG=false
RELOAD=false
WORKERS=4

# Grafana Configuration
GRAFANA_ADMIN_PASSWORD=admin123
GRAFANA_SECRET_KEY=your-grafana-secret-key

# Additional Security Headers
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
TRUSTED_HOSTS=localhost,yourdomain.com

# Feature Flags
ENABLE_ML_TRAINING=true
ENABLE_AUTOML=true
ENABLE_EDGE_DEPLOYMENT=true
ENABLE_REAL_TIME_INFERENCE=true
ENABLE_BATCH_PROCESSING=true

# Performance Tuning
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
REDIS_POOL_SIZE=10
WORKER_CONCURRENCY=4

# Kubernetes Configuration (if deploying to K8s)
KUBERNETES_NAMESPACE=ai-platform
KUBERNETES_SERVICE_ACCOUNT=ai-platform-sa
KUBERNETES_CLUSTER_NAME=ai-platform-cluster
