# AI Data Platform - Production Environment Configuration
# Copy this file to .env.prod and update with your production values

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Production Database
POSTGRES_DB=ai_platform_prod
POSTGRES_USER=ai_platform_user
POSTGRES_PASSWORD=CHANGE_ME_STRONG_PASSWORD_HERE
DATABASE_URL=**************************************************************************/ai_platform_prod

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD_HERE
REDIS_URL=redis://:CHANGE_ME_REDIS_PASSWORD_HERE@redis:6379/0

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================
CELERY_BROKER_URL=redis://:CHANGE_ME_REDIS_PASSWORD_HERE@redis:6379/1
CELERY_RESULT_BACKEND=redis://:CHANGE_ME_REDIS_PASSWORD_HERE@redis:6379/2

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate a strong secret key: openssl rand -hex 32
SECRET_KEY=CHANGE_ME_GENERATE_STRONG_SECRET_KEY_HERE
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_APP_NAME=AI Data Platform
NEXT_PUBLIC_ENVIRONMENT=production

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
UPLOAD_DIR=/app/data/uploads
MODEL_STORAGE_PATH=/app/data/models
MAX_FILE_SIZE=500MB

# =============================================================================
# AI/ML CONFIGURATION
# =============================================================================
# OpenAI API Key (optional)
OPENAI_API_KEY=your_openai_api_key_here

# Hugging Face Cache Directory
HUGGINGFACE_CACHE_DIR=/app/data/cache/huggingface

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Sentry DSN for error tracking (optional)
SENTRY_DSN=your_sentry_dsn_here

# Grafana Admin Password
GRAFANA_PASSWORD=CHANGE_ME_GRAFANA_PASSWORD_HERE

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=CHANGE_ME_SMTP_PASSWORD_HERE
SMTP_TLS=true

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
DOMAIN=yourdomain.com
API_DOMAIN=api.yourdomain.com

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1024
CELERY_WORKER_CONCURRENCY=4

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_SOCIAL_LOGIN=false
ENABLE_EMAIL_VERIFICATION=true
ENABLE_ANALYTICS=true
ENABLE_MONITORING=true
