name: Continuous Deployment

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_run:
    workflows: ["Continuous Integration"]
    types:
      - completed
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Build and Push Docker Images
  build-and-push:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'push'
    
    permissions:
      contents: read
      packages: write
    
    outputs:
      backend-image: ${{ steps.meta-backend.outputs.tags }}
      frontend-image: ${{ steps.meta-frontend.outputs.tags }}
      version: ${{ steps.meta-backend.outputs.version }}
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    # Backend Image
    - name: Extract metadata for backend
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile.prod
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    # Frontend Image
    - name: Extract metadata for frontend
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        file: ./frontend/Dockerfile.prod
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: staging
      url: https://staging.ai-platform.com
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Deploy to ECS Staging
      run: |
        # Update ECS task definition with new image
        aws ecs update-service \
          --cluster ai-platform-staging \
          --service ai-platform-backend-staging \
          --force-new-deployment
        
        aws ecs update-service \
          --cluster ai-platform-staging \
          --service ai-platform-frontend-staging \
          --force-new-deployment
    
    - name: Wait for deployment
      run: |
        aws ecs wait services-stable \
          --cluster ai-platform-staging \
          --services ai-platform-backend-staging ai-platform-frontend-staging
    
    - name: Run smoke tests
      run: |
        timeout 300 bash -c 'until curl -f https://staging-api.ai-platform.com/health; do sleep 10; done'
        timeout 300 bash -c 'until curl -f https://staging.ai-platform.com/api/health; do sleep 10; done'
    
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-push, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v')
    
    environment:
      name: production
      url: https://ai-platform.com
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Create database backup
      run: |
        aws rds create-db-snapshot \
          --db-instance-identifier ai-platform-prod \
          --db-snapshot-identifier ai-platform-prod-$(date +%Y%m%d%H%M%S)
    
    - name: Deploy to ECS Production
      run: |
        # Blue-green deployment strategy
        aws ecs update-service \
          --cluster ai-platform-production \
          --service ai-platform-backend-production \
          --force-new-deployment
        
        aws ecs update-service \
          --cluster ai-platform-production \
          --service ai-platform-frontend-production \
          --force-new-deployment
    
    - name: Wait for deployment
      run: |
        aws ecs wait services-stable \
          --cluster ai-platform-production \
          --services ai-platform-backend-production ai-platform-frontend-production
    
    - name: Run production health checks
      run: |
        timeout 300 bash -c 'until curl -f https://api.ai-platform.com/health; do sleep 10; done'
        timeout 300 bash -c 'until curl -f https://ai-platform.com/api/health; do sleep 10; done'
        
        # Run comprehensive health check
        curl -f https://api.ai-platform.com/monitoring/health
    
    - name: Update DNS (if needed)
      run: |
        # Update Route 53 records if using blue-green deployment
        echo "DNS update completed"
    
    - name: Create GitHub release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: false
    
    - name: Notify production deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#production'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: "🚀 Production deployment completed successfully!"

  # Rollback on Failure
  rollback:
    runs-on: ubuntu-latest
    needs: deploy-production
    if: failure()
    
    steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
    
    - name: Rollback deployment
      run: |
        # Get previous task definition
        PREVIOUS_TASK_DEF=$(aws ecs describe-services \
          --cluster ai-platform-production \
          --services ai-platform-backend-production \
          --query 'services[0].deployments[1].taskDefinition' \
          --output text)
        
        # Update service to previous version
        aws ecs update-service \
          --cluster ai-platform-production \
          --service ai-platform-backend-production \
          --task-definition $PREVIOUS_TASK_DEF
    
    - name: Notify rollback
      uses: 8398a7/action-slack@v3
      with:
        status: 'warning'
        channel: '#production'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        text: "⚠️ Production deployment failed. Rollback initiated."
