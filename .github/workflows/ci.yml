name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '20'
  POSTGRES_PASSWORD: postgres
  REDIS_PASSWORD: redis_test

jobs:
  # Backend Testing
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ai_platform_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov
    
    - name: Set up environment variables
      run: |
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/ai_platform_test" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/0" >> $GITHUB_ENV
        echo "SECRET_KEY=test-secret-key-for-ci" >> $GITHUB_ENV
        echo "ENVIRONMENT=test" >> $GITHUB_ENV
    
    - name: Run database migrations
      working-directory: ./backend
      run: |
        alembic upgrade head
    
    - name: Run tests with coverage
      working-directory: ./backend
      run: |
        pytest --cov=app --cov-report=xml --cov-report=html tests/
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage
    
    - name: Run security scan
      working-directory: ./backend
      run: |
        pip install bandit safety
        bandit -r app/
        safety check
    
    - name: Run linting
      working-directory: ./backend
      run: |
        pip install flake8 black isort mypy
        flake8 app/
        black --check app/
        isort --check-only app/
        mypy app/

  # Frontend Testing
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run linting
      working-directory: ./frontend
      run: npm run lint
    
    - name: Run type checking
      working-directory: ./frontend
      run: npx tsc --noEmit
    
    - name: Run tests
      working-directory: ./frontend
      run: npm test -- --coverage --watchAll=false
    
    - name: Build application
      working-directory: ./frontend
      run: npm run build
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build and start services
      run: |
        cp .env.production .env.test
        sed -i 's/CHANGE_ME_STRONG_PASSWORD_HERE/test_password/g' .env.test
        sed -i 's/CHANGE_ME_REDIS_PASSWORD_HERE/test_redis/g' .env.test
        sed -i 's/CHANGE_ME_GENERATE_STRONG_SECRET_KEY_HERE/test-secret-key/g' .env.test
        docker-compose -f docker-compose.yml --env-file .env.test up -d --build
    
    - name: Wait for services to be ready
      run: |
        timeout 300 bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'
        timeout 300 bash -c 'until curl -f http://localhost:3000/api/health; do sleep 5; done'
    
    - name: Run integration tests
      run: |
        chmod +x scripts/test-platform.sh
        ./scripts/test-platform.sh
    
    - name: Collect logs on failure
      if: failure()
      run: |
        docker-compose logs > integration-test-logs.txt
    
    - name: Upload logs
      if: failure()
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-logs
        path: integration-test-logs.txt
    
    - name: Clean up
      if: always()
      run: |
        docker-compose down -v

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: python, javascript
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v2
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  # Performance Testing
  performance-tests:
    runs-on: ubuntu-latest
    needs: [integration-tests]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Start services for performance testing
      run: |
        cp .env.production .env.perf
        sed -i 's/CHANGE_ME_STRONG_PASSWORD_HERE/perf_password/g' .env.perf
        sed -i 's/CHANGE_ME_REDIS_PASSWORD_HERE/perf_redis/g' .env.perf
        sed -i 's/CHANGE_ME_GENERATE_STRONG_SECRET_KEY_HERE/perf-secret-key/g' .env.perf
        docker-compose -f docker-compose.yml --env-file .env.perf up -d --build
    
    - name: Wait for services
      run: |
        timeout 300 bash -c 'until curl -f http://localhost:8000/health; do sleep 5; done'
    
    - name: Install k6
      run: |
        sudo gpg -k
        sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
    
    - name: Run performance tests
      run: |
        k6 run --out json=performance-results.json tests/performance/load-test.js
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json
    
    - name: Clean up
      if: always()
      run: |
        docker-compose down -v
