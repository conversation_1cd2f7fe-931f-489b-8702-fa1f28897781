# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# OS-generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.manifest
*.spec
pip-log.txt
pip-delete-this-directory.txt
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
instance/
.webassets-cache
.bundle
celerybeat-schedule
celerybeat.pid

# Virtualenv
.env
.venv
env/
venv/
ENV/
VENV/

# Project-specific virtual environments
backend/venv/
backend/lock/
ml/.venv

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
yalc.lock
.npm
.node_repl_history
*.tgz
.yarn-integrity

# Next.js
.next/
out/

# Local environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env

# Data folders
data/uploads/
data/raw/
data/processed/
data/lake/
