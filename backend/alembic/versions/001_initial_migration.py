"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create schemas
    op.execute('CREATE SCHEMA IF NOT EXISTS auth')
    op.execute('CREATE SCHEMA IF NOT EXISTS data_management')
    op.execute('CREATE SCHEMA IF NOT EXISTS ml_models')
    op.execute('CREATE SCHEMA IF NOT EXISTS workflows')
    
    # Create ENUM types
    op.execute("CREATE TYPE auth.user_role AS ENUM ('admin', 'data_scientist', 'data_engineer', 'analyst', 'viewer')")
    op.execute("CREATE TYPE data_management.project_status AS ENUM ('active', 'archived', 'deleted')")
    op.execute("CREATE TYPE data_management.project_type AS ENUM ('data_science', 'data_engineering', 'analytics', 'ml_research', 'production')")
    op.execute("CREATE TYPE data_management.data_source_type AS ENUM ('database', 'file', 'api', 'stream', 'cloud_storage')")
    op.execute("CREATE TYPE data_management.data_source_status AS ENUM ('active', 'inactive', 'error', 'testing')")
    op.execute("CREATE TYPE data_management.pipeline_status AS ENUM ('draft', 'active', 'paused', 'completed', 'failed')")
    op.execute("CREATE TYPE data_management.pipeline_type AS ENUM ('etl', 'elt', 'streaming', 'batch', 'real_time')")
    op.execute("CREATE TYPE ml_models.model_status AS ENUM ('training', 'trained', 'deployed', 'archived', 'failed')")
    op.execute("CREATE TYPE ml_models.model_type AS ENUM ('classification', 'regression', 'clustering', 'time_series', 'nlp', 'computer_vision', 'deep_learning', 'reinforcement_learning', 'generative_ai')")
    op.execute("CREATE TYPE ml_models.ml_framework AS ENUM ('scikit_learn', 'pytorch', 'tensorflow', 'xgboost', 'lightgbm', 'catboost', 'autogluon', 'huggingface', 'custom')")
    op.execute("CREATE TYPE workflows.workflow_status AS ENUM ('draft', 'active', 'paused', 'completed', 'failed')")
    op.execute("CREATE TYPE workflows.workflow_type AS ENUM ('data_pipeline', 'ml_pipeline', 'analysis_workflow', 'deployment_workflow', 'monitoring_workflow')")
    
    # Create users table
    op.create_table('users',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('username', sa.String(length=100), nullable=False),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('role', postgresql.ENUM(name='user_role', schema='auth'), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_verified', sa.Boolean(), nullable=False),
        sa.Column('avatar_url', sa.String(length=500), nullable=True),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('company', sa.String(length=255), nullable=True),
        sa.Column('location', sa.String(length=255), nullable=True),
        sa.Column('website', sa.String(length=500), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=False),
        sa.Column('language', sa.String(length=10), nullable=False),
        sa.Column('theme', sa.String(length=20), nullable=False),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('username'),
        schema='auth'
    )
    
    # Create projects table
    op.create_table('projects',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('type', postgresql.ENUM(name='project_type', schema='data_management'), nullable=False),
        sa.Column('status', postgresql.ENUM(name='project_status', schema='data_management'), nullable=False),
        sa.Column('is_public', sa.Boolean(), nullable=False),
        sa.Column('is_template', sa.Boolean(), nullable=False),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('settings', sa.JSON(), nullable=True),
        sa.Column('data_sources_count', sa.Integer(), nullable=False),
        sa.Column('pipelines_count', sa.Integer(), nullable=False),
        sa.Column('models_count', sa.Integer(), nullable=False),
        sa.Column('workflows_count', sa.Integer(), nullable=False),
        sa.Column('owner_id', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['owner_id'], ['auth.users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='data_management'
    )
    
    # Create data_sources table
    op.create_table('data_sources',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('type', postgresql.ENUM(name='data_source_type', schema='data_management'), nullable=False),
        sa.Column('status', postgresql.ENUM(name='data_source_status', schema='data_management'), nullable=False),
        sa.Column('connection_config', sa.JSON(), nullable=False),
        sa.Column('credentials', sa.JSON(), nullable=True),
        sa.Column('schema_info', sa.JSON(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=False),
        sa.Column('auto_sync', sa.Boolean(), nullable=False),
        sa.Column('sync_frequency', sa.String(length=50), nullable=True),
        sa.Column('last_sync', sa.String(length=50), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('created_by_id', sa.String(), nullable=False),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by_id'], ['auth.users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['data_management.projects.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='data_management'
    )
    
    # Create pipelines table
    op.create_table('pipelines',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('type', postgresql.ENUM(name='pipeline_type', schema='data_management'), nullable=False),
        sa.Column('status', postgresql.ENUM(name='pipeline_status', schema='data_management'), nullable=False),
        sa.Column('definition', sa.JSON(), nullable=False),
        sa.Column('config', sa.JSON(), nullable=True),
        sa.Column('schedule_expression', sa.String(length=100), nullable=True),
        sa.Column('is_scheduled', sa.Boolean(), nullable=False),
        sa.Column('last_run_start', sa.String(length=50), nullable=True),
        sa.Column('last_run_end', sa.String(length=50), nullable=True),
        sa.Column('last_run_status', sa.String(length=50), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('total_runs', sa.Integer(), nullable=False),
        sa.Column('successful_runs', sa.Integer(), nullable=False),
        sa.Column('failed_runs', sa.Integer(), nullable=False),
        sa.Column('created_by_id', sa.String(), nullable=False),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by_id'], ['auth.users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['data_management.projects.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='data_management'
    )
    
    # Create ml_models table
    op.create_table('ml_models',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('model_type', postgresql.ENUM(name='model_type', schema='ml_models'), nullable=False),
        sa.Column('framework', postgresql.ENUM(name='ml_framework', schema='ml_models'), nullable=False),
        sa.Column('algorithm', sa.String(length=100), nullable=True),
        sa.Column('status', postgresql.ENUM(name='model_status', schema='ml_models'), nullable=False),
        sa.Column('training_config', sa.JSON(), nullable=False),
        sa.Column('hyperparameters', sa.JSON(), nullable=True),
        sa.Column('feature_config', sa.JSON(), nullable=True),
        sa.Column('model_path', sa.String(length=500), nullable=True),
        sa.Column('model_size_mb', sa.Float(), nullable=True),
        sa.Column('model_format', sa.String(length=50), nullable=True),
        sa.Column('metrics', sa.JSON(), nullable=True),
        sa.Column('validation_score', sa.Float(), nullable=True),
        sa.Column('test_score', sa.Float(), nullable=True),
        sa.Column('training_dataset_id', sa.String(), nullable=True),
        sa.Column('training_samples', sa.Integer(), nullable=True),
        sa.Column('training_duration_seconds', sa.Integer(), nullable=True),
        sa.Column('training_start', sa.String(length=50), nullable=True),
        sa.Column('training_end', sa.String(length=50), nullable=True),
        sa.Column('deployment_config', sa.JSON(), nullable=True),
        sa.Column('endpoint_url', sa.String(length=500), nullable=True),
        sa.Column('deployment_status', sa.String(length=50), nullable=True),
        sa.Column('prediction_count', sa.Integer(), nullable=False),
        sa.Column('last_prediction', sa.String(length=50), nullable=True),
        sa.Column('drift_score', sa.Float(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_by_id', sa.String(), nullable=False),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('pipeline_id', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by_id'], ['auth.users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['data_management.projects.id'], ),
        sa.ForeignKeyConstraint(['pipeline_id'], ['workflows.pipelines.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='ml_models'
    )
    
    # Create workflows table
    op.create_table('workflows',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('type', postgresql.ENUM(name='workflow_type', schema='workflows'), nullable=False),
        sa.Column('status', postgresql.ENUM(name='workflow_status', schema='workflows'), nullable=False),
        sa.Column('definition', sa.JSON(), nullable=False),
        sa.Column('nodes', sa.JSON(), nullable=False),
        sa.Column('edges', sa.JSON(), nullable=False),
        sa.Column('config', sa.JSON(), nullable=True),
        sa.Column('variables', sa.JSON(), nullable=True),
        sa.Column('is_scheduled', sa.Boolean(), nullable=False),
        sa.Column('schedule_expression', sa.String(length=100), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=False),
        sa.Column('max_retries', sa.Integer(), nullable=False),
        sa.Column('timeout_minutes', sa.Integer(), nullable=True),
        sa.Column('parallel_execution', sa.Boolean(), nullable=False),
        sa.Column('last_execution_id', sa.String(), nullable=True),
        sa.Column('last_execution_status', sa.String(length=50), nullable=True),
        sa.Column('last_execution_start', sa.String(length=50), nullable=True),
        sa.Column('last_execution_end', sa.String(length=50), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('total_executions', sa.Integer(), nullable=False),
        sa.Column('successful_executions', sa.Integer(), nullable=False),
        sa.Column('failed_executions', sa.Integer(), nullable=False),
        sa.Column('avg_duration_seconds', sa.Float(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=False),
        sa.Column('is_template', sa.Boolean(), nullable=False),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('created_by_id', sa.String(), nullable=False),
        sa.Column('project_id', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by_id'], ['auth.users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['data_management.projects.id'], ),
        sa.PrimaryKeyConstraint('id'),
        schema='workflows'
    )


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('workflows', schema='workflows')
    op.drop_table('ml_models', schema='ml_models')
    op.drop_table('pipelines', schema='data_management')
    op.drop_table('data_sources', schema='data_management')
    op.drop_table('projects', schema='data_management')
    op.drop_table('users', schema='auth')
    
    # Drop ENUM types
    op.execute("DROP TYPE IF EXISTS workflows.workflow_type")
    op.execute("DROP TYPE IF EXISTS workflows.workflow_status")
    op.execute("DROP TYPE IF EXISTS ml_models.ml_framework")
    op.execute("DROP TYPE IF EXISTS ml_models.model_type")
    op.execute("DROP TYPE IF EXISTS ml_models.model_status")
    op.execute("DROP TYPE IF EXISTS data_management.pipeline_type")
    op.execute("DROP TYPE IF EXISTS data_management.pipeline_status")
    op.execute("DROP TYPE IF EXISTS data_management.data_source_status")
    op.execute("DROP TYPE IF EXISTS data_management.data_source_type")
    op.execute("DROP TYPE IF EXISTS data_management.project_type")
    op.execute("DROP TYPE IF EXISTS data_management.project_status")
    op.execute("DROP TYPE IF EXISTS auth.user_role")
    
    # Drop schemas
    op.execute('DROP SCHEMA IF EXISTS workflows')
    op.execute('DROP SCHEMA IF EXISTS ml_models')
    op.execute('DROP SCHEMA IF EXISTS data_management')
    op.execute('DROP SCHEMA IF EXISTS auth')
