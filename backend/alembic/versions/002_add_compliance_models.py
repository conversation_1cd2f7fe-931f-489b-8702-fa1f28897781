"""Add compliance models for GDPR and audit

Revision ID: 002
Revises: 001
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create consent_records table
    op.create_table('consent_records',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('purpose', sa.String(length=255), nullable=False),
        sa.Column('consent_given', sa.<PERSON>(), nullable=False),
        sa.Column('consent_date', sa.DateTime(), nullable=False),
        sa.Column('consent_method', sa.String(length=100), nullable=False),
        sa.Column('consent_evidence', sa.Text(), nullable=True),
        sa.Column('legal_basis', sa.String(length=50), nullable=False),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('withdrawn_at', sa.DateTime(), nullable=True),
        sa.Column('withdrawal_method', sa.String(length=100), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_consent_user_purpose', 'consent_records', ['user_id', 'purpose'])
    op.create_index('idx_consent_date', 'consent_records', ['consent_date'])

    # Create data_processing_records table
    op.create_table('data_processing_records',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('processing_purpose', sa.String(length=255), nullable=False),
        sa.Column('data_categories', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('legal_basis', sa.String(length=50), nullable=False),
        sa.Column('retention_period', sa.String(length=100), nullable=True),
        sa.Column('recipients', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('third_country_transfers', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('security_measures', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_processing_user', 'data_processing_records', ['user_id'])
    op.create_index('idx_processing_purpose', 'data_processing_records', ['processing_purpose'])
    op.create_index('idx_processing_created', 'data_processing_records', ['created_at'])

    # Create data_subject_requests table
    op.create_table('data_subject_requests',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('request_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('request_date', sa.DateTime(), nullable=False),
        sa.Column('completion_date', sa.DateTime(), nullable=True),
        sa.Column('request_details', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('response_data', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('verification_method', sa.String(length=100), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_dsr_user', 'data_subject_requests', ['user_id'])
    op.create_index('idx_dsr_type_status', 'data_subject_requests', ['request_type', 'status'])
    op.create_index('idx_dsr_date', 'data_subject_requests', ['request_date'])

    # Create audit_logs table
    op.create_table('audit_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('action', sa.String(length=100), nullable=False),
        sa.Column('resource_type', sa.String(length=50), nullable=True),
        sa.Column('resource_id', sa.String(length=255), nullable=True),
        sa.Column('details', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('session_id', sa.String(length=255), nullable=True),
        sa.Column('request_id', sa.String(length=255), nullable=True),
        sa.Column('compliance_category', sa.String(length=50), nullable=True),
        sa.Column('risk_level', sa.String(length=20), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_audit_user', 'audit_logs', ['user_id'])
    op.create_index('idx_audit_action', 'audit_logs', ['action'])
    op.create_index('idx_audit_timestamp', 'audit_logs', ['timestamp'])
    op.create_index('idx_audit_resource', 'audit_logs', ['resource_type', 'resource_id'])
    op.create_index('idx_audit_compliance', 'audit_logs', ['compliance_category'])

    # Create data_breach_incidents table
    op.create_table('data_breach_incidents',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('incident_id', sa.String(length=50), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('discovered_at', sa.DateTime(), nullable=False),
        sa.Column('reported_at', sa.DateTime(), nullable=True),
        sa.Column('contained_at', sa.DateTime(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('affected_data_types', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('affected_users_count', sa.String(length=50), nullable=True),
        sa.Column('potential_impact', sa.Text(), nullable=True),
        sa.Column('root_cause', sa.Text(), nullable=True),
        sa.Column('containment_actions', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('notification_actions', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('remediation_actions', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('authority_notified', sa.Boolean(), nullable=True),
        sa.Column('users_notified', sa.Boolean(), nullable=True),
        sa.Column('notification_deadline', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('incident_id')
    )
    op.create_index('idx_breach_severity', 'data_breach_incidents', ['severity'])
    op.create_index('idx_breach_status', 'data_breach_incidents', ['status'])
    op.create_index('idx_breach_discovered', 'data_breach_incidents', ['discovered_at'])

    # Create compliance_assessments table
    op.create_table('compliance_assessments',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('assessment_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('started_at', sa.DateTime(), nullable=False),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('assessor', sa.String(length=255), nullable=True),
        sa.Column('overall_score', sa.String(length=20), nullable=True),
        sa.Column('findings', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('recommendations', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('action_items', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('evidence_collected', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('report_url', sa.String(length=500), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_assessment_type', 'compliance_assessments', ['assessment_type'])
    op.create_index('idx_assessment_status', 'compliance_assessments', ['status'])
    op.create_index('idx_assessment_date', 'compliance_assessments', ['started_at'])

    # Create retention_policies table
    op.create_table('retention_policies',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('data_category', sa.String(length=100), nullable=False),
        sa.Column('retention_period', sa.String(length=100), nullable=False),
        sa.Column('legal_basis', sa.String(length=255), nullable=True),
        sa.Column('deletion_method', sa.String(length=100), nullable=True),
        sa.Column('exceptions', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('effective_date', sa.DateTime(), nullable=False),
        sa.Column('review_date', sa.DateTime(), nullable=True),
        sa.Column('approved_by', sa.String(length=255), nullable=True),
        sa.Column('approval_date', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_retention_category', 'retention_policies', ['data_category'])
    op.create_index('idx_retention_effective', 'retention_policies', ['effective_date'])

    # Create privacy_impact_assessments table
    op.create_table('privacy_impact_assessments',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('project_name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False),
        sa.Column('data_types', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('processing_purposes', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('legal_basis', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('data_subjects', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('recipients', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('privacy_risks', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('risk_mitigation', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('residual_risks', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('conducted_by', sa.String(length=255), nullable=True),
        sa.Column('reviewed_by', sa.String(length=255), nullable=True),
        sa.Column('approved_by', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('review_date', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_pia_status', 'privacy_impact_assessments', ['status'])
    op.create_index('idx_pia_created', 'privacy_impact_assessments', ['created_at'])


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('privacy_impact_assessments')
    op.drop_table('retention_policies')
    op.drop_table('compliance_assessments')
    op.drop_table('data_breach_incidents')
    op.drop_table('audit_logs')
    op.drop_table('data_subject_requests')
    op.drop_table('data_processing_records')
    op.drop_table('consent_records')
