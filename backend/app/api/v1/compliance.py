"""
Compliance API endpoints for GDPR, SOC2, and audit management
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import structlog

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.compliance import (
    ConsentRecord, DataProcessingRecord, DataSubjectRequest,
    AuditLog, DataBreachIncident, ComplianceAssessment,
    RetentionPolicy, PrivacyImpactAssessment
)
from app.core.compliance import gdpr_compliance
from app.core.error_handlers import NotFoundError, ValidationError, AuthorizationError

router = APIRouter()
logger = structlog.get_logger()

# Pydantic models
class ConsentRequest(BaseModel):
    purpose: str = Field(..., description="Purpose of data processing")
    consent_given: bool = Field(..., description="Whether consent is given")
    consent_method: str = Field(..., description="Method of consent collection")
    consent_evidence: str = Field(..., description="Evidence of consent")
    legal_basis: str = Field(default="consent", description="GDPR legal basis")

class DataSubjectRequestCreate(BaseModel):
    request_type: str = Field(..., description="Type of request (access, rectification, erasure, etc.)")
    request_details: Dict[str, Any] = Field(default_factory=dict, description="Specific details")
    verification_method: Optional[str] = Field(None, description="Identity verification method")

class DataBreachReport(BaseModel):
    severity: str = Field(..., description="Severity level (low, medium, high, critical)")
    description: str = Field(..., description="Description of the breach")
    affected_data_types: List[str] = Field(..., description="Types of data affected")
    affected_users_count: str = Field(..., description="Number of affected users")
    potential_impact: str = Field(..., description="Potential impact assessment")

class ComplianceMetrics(BaseModel):
    total_consents: int
    active_consents: int
    withdrawn_consents: int
    pending_dsr: int
    completed_dsr: int
    open_breaches: int
    compliance_score: float

# Consent Management
@router.post("/consent")
async def record_consent(
    request: ConsentRequest,
    http_request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Record user consent for data processing"""
    
    try:
        # Extract request metadata
        ip_address = http_request.client.host
        user_agent = http_request.headers.get("user-agent", "")
        
        # Create consent record
        consent_record = ConsentRecord(
            user_id=current_user.id,
            purpose=request.purpose,
            consent_given=request.consent_given,
            consent_method=request.consent_method,
            consent_evidence=request.consent_evidence,
            legal_basis=request.legal_basis,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        db.add(consent_record)
        await db.commit()
        await db.refresh(consent_record)
        
        # Log audit trail
        await log_audit_event(
            db=db,
            user_id=current_user.id,
            action="consent_recorded",
            resource_type="consent",
            resource_id=str(consent_record.id),
            details={
                "purpose": request.purpose,
                "consent_given": request.consent_given,
                "method": request.consent_method
            },
            ip_address=ip_address,
            user_agent=user_agent,
            compliance_category="GDPR"
        )
        
        logger.info(
            "Consent recorded",
            consent_id=consent_record.id,
            user_id=current_user.id,
            purpose=request.purpose,
            consent_given=request.consent_given
        )
        
        return {
            "consent_id": consent_record.id,
            "status": "recorded",
            "timestamp": consent_record.consent_date
        }
        
    except Exception as e:
        logger.error("Failed to record consent", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record consent"
        )

@router.get("/consent")
async def get_user_consents(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's consent records"""
    
    result = await db.execute(
        select(ConsentRecord)
        .where(ConsentRecord.user_id == current_user.id)
        .order_by(ConsentRecord.consent_date.desc())
    )
    consents = result.scalars().all()
    
    return {
        "consents": [
            {
                "id": consent.id,
                "purpose": consent.purpose,
                "consent_given": consent.consent_given,
                "consent_date": consent.consent_date,
                "legal_basis": consent.legal_basis,
                "withdrawn_at": consent.withdrawn_at
            }
            for consent in consents
        ]
    }

@router.post("/consent/{consent_id}/withdraw")
async def withdraw_consent(
    consent_id: str,
    http_request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Withdraw user consent"""
    
    # Find consent record
    result = await db.execute(
        select(ConsentRecord)
        .where(
            and_(
                ConsentRecord.id == consent_id,
                ConsentRecord.user_id == current_user.id
            )
        )
    )
    consent = result.scalar_one_or_none()
    
    if not consent:
        raise NotFoundError("Consent record", consent_id)
    
    if consent.withdrawn_at:
        raise ValidationError("Consent already withdrawn")
    
    # Withdraw consent
    consent.withdrawn_at = datetime.utcnow()
    consent.withdrawal_method = "user_request"
    
    await db.commit()
    
    # Log audit trail
    await log_audit_event(
        db=db,
        user_id=current_user.id,
        action="consent_withdrawn",
        resource_type="consent",
        resource_id=consent_id,
        details={"purpose": consent.purpose},
        ip_address=http_request.client.host,
        user_agent=http_request.headers.get("user-agent", ""),
        compliance_category="GDPR"
    )
    
    logger.info(
        "Consent withdrawn",
        consent_id=consent_id,
        user_id=current_user.id,
        purpose=consent.purpose
    )
    
    return {
        "status": "withdrawn",
        "withdrawn_at": consent.withdrawn_at
    }

# Data Subject Requests
@router.post("/data-subject-request")
async def create_data_subject_request(
    request: DataSubjectRequestCreate,
    http_request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a data subject request (GDPR Article 15-22)"""
    
    # Validate request type
    valid_types = ["access", "rectification", "erasure", "portability", "restriction", "objection"]
    if request.request_type not in valid_types:
        raise ValidationError(f"Invalid request type. Must be one of: {', '.join(valid_types)}")
    
    # Create DSR
    dsr = DataSubjectRequest(
        user_id=current_user.id,
        request_type=request.request_type,
        request_details=request.request_details,
        verification_method=request.verification_method or "authenticated_session"
    )
    
    db.add(dsr)
    await db.commit()
    await db.refresh(dsr)
    
    # Log audit trail
    await log_audit_event(
        db=db,
        user_id=current_user.id,
        action="dsr_created",
        resource_type="data_subject_request",
        resource_id=str(dsr.id),
        details={
            "request_type": request.request_type,
            "details": request.request_details
        },
        ip_address=http_request.client.host,
        user_agent=http_request.headers.get("user-agent", ""),
        compliance_category="GDPR"
    )
    
    logger.info(
        "Data subject request created",
        dsr_id=dsr.id,
        user_id=current_user.id,
        request_type=request.request_type
    )
    
    return {
        "request_id": dsr.id,
        "status": dsr.status,
        "request_date": dsr.request_date,
        "estimated_completion": dsr.request_date + timedelta(days=30)  # GDPR requirement
    }

@router.get("/data-subject-requests")
async def get_user_data_subject_requests(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's data subject requests"""
    
    result = await db.execute(
        select(DataSubjectRequest)
        .where(DataSubjectRequest.user_id == current_user.id)
        .order_by(DataSubjectRequest.request_date.desc())
    )
    requests = result.scalars().all()
    
    return {
        "requests": [
            {
                "id": req.id,
                "request_type": req.request_type,
                "status": req.status,
                "request_date": req.request_date,
                "completion_date": req.completion_date,
                "request_details": req.request_details
            }
            for req in requests
        ]
    }

# Data Export (Right to Portability)
@router.get("/data-export")
async def export_user_data(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Export all user data (GDPR Article 20)"""
    
    # This would collect all user data from various tables
    # For demo, returning basic user info
    user_data = {
        "user_profile": {
            "id": str(current_user.id),
            "username": current_user.username,
            "email": current_user.email,
            "full_name": current_user.full_name,
            "created_at": current_user.created_at.isoformat(),
            "last_login": current_user.last_login.isoformat() if current_user.last_login else None
        },
        "consents": [],
        "data_subject_requests": [],
        "audit_logs": []
    }
    
    # Get consents
    consents_result = await db.execute(
        select(ConsentRecord).where(ConsentRecord.user_id == current_user.id)
    )
    consents = consents_result.scalars().all()
    user_data["consents"] = [
        {
            "purpose": consent.purpose,
            "consent_given": consent.consent_given,
            "consent_date": consent.consent_date.isoformat(),
            "legal_basis": consent.legal_basis
        }
        for consent in consents
    ]
    
    # Log the export request
    await log_audit_event(
        db=db,
        user_id=current_user.id,
        action="data_exported",
        resource_type="user_data",
        resource_id=str(current_user.id),
        details={"export_type": "full_export"},
        compliance_category="GDPR"
    )
    
    return {
        "export_date": datetime.utcnow().isoformat(),
        "data": user_data
    }

# Compliance Metrics (Admin only)
@router.get("/metrics", dependencies=[Depends(require_permission("view_compliance"))])
async def get_compliance_metrics(
    db: AsyncSession = Depends(get_db)
) -> ComplianceMetrics:
    """Get compliance metrics and KPIs"""
    
    # Count consents
    total_consents_result = await db.execute(select(func.count(ConsentRecord.id)))
    total_consents = total_consents_result.scalar()
    
    active_consents_result = await db.execute(
        select(func.count(ConsentRecord.id))
        .where(and_(ConsentRecord.consent_given == True, ConsentRecord.withdrawn_at.is_(None)))
    )
    active_consents = active_consents_result.scalar()
    
    withdrawn_consents_result = await db.execute(
        select(func.count(ConsentRecord.id))
        .where(ConsentRecord.withdrawn_at.isnot(None))
    )
    withdrawn_consents = withdrawn_consents_result.scalar()
    
    # Count DSRs
    pending_dsr_result = await db.execute(
        select(func.count(DataSubjectRequest.id))
        .where(DataSubjectRequest.status == "pending")
    )
    pending_dsr = pending_dsr_result.scalar()
    
    completed_dsr_result = await db.execute(
        select(func.count(DataSubjectRequest.id))
        .where(DataSubjectRequest.status == "completed")
    )
    completed_dsr = completed_dsr_result.scalar()
    
    # Count open breaches
    open_breaches_result = await db.execute(
        select(func.count(DataBreachIncident.id))
        .where(DataBreachIncident.status.in_(["investigating", "containing", "notifying"]))
    )
    open_breaches = open_breaches_result.scalar()
    
    # Calculate compliance score (simplified)
    compliance_score = 95.0  # Would be calculated based on various factors
    
    return ComplianceMetrics(
        total_consents=total_consents or 0,
        active_consents=active_consents or 0,
        withdrawn_consents=withdrawn_consents or 0,
        pending_dsr=pending_dsr or 0,
        completed_dsr=completed_dsr or 0,
        open_breaches=open_breaches or 0,
        compliance_score=compliance_score
    )

# Utility function for audit logging
async def log_audit_event(
    db: AsyncSession,
    user_id: str,
    action: str,
    resource_type: str = None,
    resource_id: str = None,
    details: Dict[str, Any] = None,
    ip_address: str = None,
    user_agent: str = None,
    compliance_category: str = None,
    risk_level: str = "low"
):
    """Log an audit event"""
    
    audit_log = AuditLog(
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        details=details or {},
        ip_address=ip_address,
        user_agent=user_agent,
        compliance_category=compliance_category,
        risk_level=risk_level
    )
    
    db.add(audit_log)
    await db.commit()
