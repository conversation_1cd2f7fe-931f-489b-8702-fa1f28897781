from fastapi import APIRouter, Depends, Query, UploadFile, File, Form, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import pandas as pd
import json
import os
import aiofiles
import structlog
from pathlib import Path
from datetime import datetime
import uuid

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.data_source import DataSource, DataSourceType, DataSourceStatus
from app.models.project import Project
from app.core.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.core.config import settings
from app.core.compliance import gdpr_compliance

router = APIRouter()


# Pydantic models
class DataSourceCreate(BaseModel):
    name: str
    description: Optional[str] = None
    type: DataSourceType
    connection_config: Dict[str, Any]
    credentials: Optional[Dict[str, Any]] = None
    project_id: Optional[str] = None
    is_public: bool = False
    auto_sync: bool = False
    sync_frequency: Optional[str] = None


class DataSourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    connection_config: Optional[Dict[str, Any]] = None
    credentials: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    auto_sync: Optional[bool] = None
    sync_frequency: Optional[str] = None


class DataSourceResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    type: DataSourceType
    status: DataSourceStatus
    connection_config: Dict[str, Any]
    schema_info: Optional[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]]
    is_public: bool
    auto_sync: bool
    sync_frequency: Optional[str]
    last_sync: Optional[str]
    last_error: Optional[str]
    is_healthy: bool
    created_by_id: str
    project_id: Optional[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class DataSourceListResponse(BaseModel):
    data_sources: List[DataSourceResponse]
    total: int
    page: int
    size: int


class FileUploadResponse(BaseModel):
    filename: str
    file_path: str
    file_size: int
    file_type: str
    columns: Optional[List[str]] = None
    rows: Optional[int] = None
    preview: Optional[List[Dict[str, Any]]] = None


@router.post("/sources", response_model=DataSourceResponse)
async def create_data_source(
    data_source_data: DataSourceCreate,
    current_user: User = Depends(require_permission("manage_data")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new data source"""

    # Validate project access if project_id is provided
    if data_source_data.project_id:
        result = await db.execute(select(Project).where(Project.id == data_source_data.project_id))
        project = result.scalar_one_or_none()

        if not project:
            raise NotFoundError("Project not found")

        if (project.owner_id != current_user.id and
            not project.is_public and
            not current_user.is_admin):
            raise AuthorizationError("Access denied to project")

    new_data_source = DataSource(
        name=data_source_data.name,
        description=data_source_data.description,
        type=data_source_data.type,
        connection_config=data_source_data.connection_config,
        credentials=data_source_data.credentials,  # TODO: Encrypt credentials
        project_id=data_source_data.project_id,
        is_public=data_source_data.is_public,
        auto_sync=data_source_data.auto_sync,
        sync_frequency=data_source_data.sync_frequency,
        created_by_id=current_user.id,
        status=DataSourceStatus.TESTING
    )

    db.add(new_data_source)
    await db.commit()
    await db.refresh(new_data_source)

    return new_data_source


@router.get("/sources", response_model=DataSourceListResponse)
async def list_data_sources(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    type: Optional[DataSourceType] = None,
    status: Optional[DataSourceStatus] = None,
    project_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List data sources"""

    # Build query
    query = select(DataSource)

    # Filter by access permissions
    if not current_user.is_admin:
        query = query.where(
            (DataSource.created_by_id == current_user.id) |
            (DataSource.is_public == True)
        )

    # Apply filters
    if search:
        query = query.where(
            (DataSource.name.ilike(f"%{search}%")) |
            (DataSource.description.ilike(f"%{search}%"))
        )

    if type:
        query = query.where(DataSource.type == type)

    if status:
        query = query.where(DataSource.status == status)

    if project_id:
        query = query.where(DataSource.project_id == project_id)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)

    # Execute query
    result = await db.execute(query)
    data_sources = result.scalars().all()

    return DataSourceListResponse(
        data_sources=data_sources,
        total=total,
        page=page,
        size=size
    )


@router.get("/sources/{data_source_id}", response_model=DataSourceResponse)
async def get_data_source(
    data_source_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get data source by ID"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check access permissions
    if (data_source.created_by_id != current_user.id and
        not data_source.is_public and
        not current_user.is_admin):
        raise AuthorizationError("Access denied")

    return data_source


@router.put("/sources/{data_source_id}", response_model=DataSourceResponse)
async def update_data_source(
    data_source_id: str,
    data_source_update: DataSourceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update data source"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check ownership
    if data_source.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only data source owner can update")

    # Update fields
    for field, value in data_source_update.dict(exclude_unset=True).items():
        setattr(data_source, field, value)

    await db.commit()
    await db.refresh(data_source)

    return data_source


@router.delete("/sources/{data_source_id}")
async def delete_data_source(
    data_source_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete data source"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check ownership
    if data_source.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only data source owner can delete")

    await db.delete(data_source)
    await db.commit()

    return {"message": "Data source deleted successfully"}


logger = structlog.get_logger()

class FileUploadResponse(BaseModel):
    filename: str
    file_path: str
    file_size: int
    file_type: str
    columns: Optional[List[str]] = None
    rows: Optional[int] = None
    preview: Optional[List[Dict[str, Any]]] = None
    analysis_error: Optional[str] = None
    data_source_id: Optional[str] = None
    contains_pii: bool = False
    gdpr_compliance_status: str = "pending"

@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    project_id: Optional[str] = Form(None),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(require_permission("manage_data")),
    db: AsyncSession = Depends(get_db)
):
    """Enhanced file upload with GDPR compliance and async processing"""

    try:
        # Validate file size (100MB limit)
        max_size = 100 * 1024 * 1024  # 100MB
        if file.size and file.size > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="File size exceeds 100MB limit"
            )

        # Validate file type
        allowed_extensions = {'.csv', '.json', '.xlsx', '.parquet', '.txt'}
        file_extension = Path(file.filename).suffix.lower()

        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_extension} not supported. Allowed: {', '.join(allowed_extensions)}"
            )

        # Validate project access if specified
        if project_id:
            result = await db.execute(select(Project).where(Project.id == project_id))
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Project not found"
                )

            if (project.owner_id != current_user.id and
                not project.is_public and
                not current_user.is_admin):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to project"
                )

        # Create upload directory if it doesn't exist
        upload_dir = Path(settings.UPLOAD_DIR) / str(current_user.id)
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename with timestamp
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        unique_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}_{file.filename}"
        file_path = upload_dir / unique_filename

        # Save file asynchronously
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)

        file_size = len(content)

        # Basic file info
        file_info = {
            "filename": file.filename,
            "file_path": str(file_path),
            "file_size": file_size,
            "file_type": file_extension
        }

        # Analyze file structure and detect PII
        analysis_result = await analyze_file_async(file_path, file_extension)
        file_info.update(analysis_result)

        # Create data source record
        data_source = DataSource(
            name=f"Uploaded: {file.filename}",
            description=f"File uploaded by {current_user.username}",
            type=DataSourceType.FILE,
            connection_config={
                "file_path": str(file_path),
                "file_type": file_extension,
                "original_filename": file.filename
            },
            project_id=project_id,
            created_by_id=current_user.id,
            status=DataSourceStatus.ACTIVE,
            metadata={
                "upload_timestamp": datetime.utcnow().isoformat(),
                "file_size": file_size,
                "columns": analysis_result.get("columns"),
                "rows": analysis_result.get("rows"),
                "contains_pii": analysis_result.get("contains_pii", False)
            }
        )

        db.add(data_source)
        await db.commit()
        await db.refresh(data_source)

        file_info["data_source_id"] = data_source.id

        # Record GDPR compliance if PII detected
        if analysis_result.get("contains_pii", False):
            consent_id = gdpr_compliance.record_consent(
                user_id=current_user.id,
                purpose="data_processing",
                consent_given=True,
                consent_method="file_upload",
                consent_evidence=f"User uploaded file {file.filename} containing PII",
                legal_basis="legitimate_interest"
            )

            file_info["gdpr_compliance_status"] = "recorded"

            # Schedule PII analysis in background
            background_tasks.add_task(
                analyze_pii_compliance,
                data_source.id,
                str(file_path)
            )

        logger.info(
            "File uploaded successfully",
            filename=file.filename,
            file_size=file_size,
            user_id=current_user.id,
            data_source_id=data_source.id,
            contains_pii=analysis_result.get("contains_pii", False)
        )

        return FileUploadResponse(**file_info)

    except HTTPException:
        raise
    except Exception as e:
        logger.error("File upload failed", error=str(e), filename=file.filename)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


async def analyze_file_async(file_path: Path, file_extension: str) -> Dict[str, Any]:
    """Asynchronously analyze uploaded file"""

    analysis = {
        "columns": None,
        "rows": None,
        "preview": None,
        "contains_pii": False,
        "analysis_error": None
    }

    try:
        if file_extension == '.csv':
            df = pd.read_csv(file_path, nrows=1000)  # Read first 1000 rows
            analysis["columns"] = df.columns.tolist()
            analysis["rows"] = len(df)
            analysis["preview"] = df.head(5).to_dict('records')
            analysis["contains_pii"] = detect_pii_in_dataframe(df)

        elif file_extension == '.json':
            with open(file_path, 'r') as f:
                data = json.load(f)
            if isinstance(data, list) and len(data) > 0:
                analysis["columns"] = list(data[0].keys()) if isinstance(data[0], dict) else None
                analysis["rows"] = len(data)
                analysis["preview"] = data[:5]
                analysis["contains_pii"] = detect_pii_in_json(data[:100])  # Check first 100 records

        elif file_extension == '.xlsx':
            df = pd.read_excel(file_path, nrows=1000)
            analysis["columns"] = df.columns.tolist()
            analysis["rows"] = len(df)
            analysis["preview"] = df.head(5).to_dict('records')
            analysis["contains_pii"] = detect_pii_in_dataframe(df)

        elif file_extension == '.parquet':
            df = pd.read_parquet(file_path)
            analysis["columns"] = df.columns.tolist()
            analysis["rows"] = len(df)
            analysis["preview"] = df.head(5).to_dict('records')
            analysis["contains_pii"] = detect_pii_in_dataframe(df)

    except Exception as e:
        analysis["analysis_error"] = str(e)
        logger.warning("File analysis failed", error=str(e), file_path=str(file_path))

    return analysis


def detect_pii_in_dataframe(df: pd.DataFrame) -> bool:
    """Detect potential PII in DataFrame columns"""

    pii_indicators = [
        'email', 'phone', 'ssn', 'social_security', 'passport', 'license',
        'address', 'name', 'first_name', 'last_name', 'full_name',
        'birth_date', 'dob', 'date_of_birth', 'credit_card', 'bank_account'
    ]

    column_names = [col.lower() for col in df.columns]

    # Check column names for PII indicators
    for indicator in pii_indicators:
        if any(indicator in col_name for col_name in column_names):
            return True

    # Check for email patterns in data
    for col in df.select_dtypes(include=['object']).columns:
        sample_values = df[col].dropna().astype(str).head(10)
        for value in sample_values:
            if '@' in value and '.' in value:  # Simple email detection
                return True

    return False


def detect_pii_in_json(data: List[Dict]) -> bool:
    """Detect potential PII in JSON data"""

    if not data or not isinstance(data[0], dict):
        return False

    pii_indicators = [
        'email', 'phone', 'ssn', 'social_security', 'passport', 'license',
        'address', 'name', 'first_name', 'last_name', 'full_name',
        'birth_date', 'dob', 'date_of_birth', 'credit_card', 'bank_account'
    ]

    # Check keys for PII indicators
    keys = list(data[0].keys())
    key_names = [key.lower() for key in keys]

    for indicator in pii_indicators:
        if any(indicator in key_name for key_name in key_names):
            return True

    # Check values for email patterns
    for record in data[:10]:  # Check first 10 records
        for value in record.values():
            if isinstance(value, str) and '@' in value and '.' in value:
                return True

    return False


async def analyze_pii_compliance(data_source_id: str, file_path: str):
    """Background task to analyze PII compliance"""

    try:
        # Detailed PII analysis would go here
        # This could include:
        # - Column-by-column PII detection
        # - Data masking recommendations
        # - Retention policy suggestions
        # - Anonymization options

        logger.info(
            "PII compliance analysis completed",
            data_source_id=data_source_id
        )

    except Exception as e:
        logger.error(
            "PII compliance analysis failed",
            data_source_id=data_source_id,
            error=str(e)
        )


@router.post("/sources/{data_source_id}/test")
async def test_data_source_connection(
    data_source_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Test data source connection"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check access permissions
    if (data_source.created_by_id != current_user.id and
        not data_source.is_public and
        not current_user.is_admin):
        raise AuthorizationError("Access denied")

    # TODO: Implement actual connection testing based on data source type
    # This is a placeholder implementation

    try:
        # Simulate connection test
        data_source.status = DataSourceStatus.ACTIVE
        data_source.last_error = None
        await db.commit()

        return {
            "status": "success",
            "message": "Connection test successful",
            "connection_string": data_source.get_connection_string()
        }
    except Exception as e:
        data_source.status = DataSourceStatus.ERROR
        data_source.last_error = str(e)
        await db.commit()

        return {
            "status": "error",
            "message": f"Connection test failed: {str(e)}"
        }