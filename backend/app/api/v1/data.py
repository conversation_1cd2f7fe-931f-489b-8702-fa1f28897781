from fastapi import APIRouter, Depends, Query, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import pandas as pd
import json
import os
from pathlib import Path

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.data_source import DataSource, DataSourceType, DataSourceStatus
from app.models.project import Project
from app.core.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.core.config import settings

router = APIRouter()


# Pydantic models
class DataSourceCreate(BaseModel):
    name: str
    description: Optional[str] = None
    type: DataSourceType
    connection_config: Dict[str, Any]
    credentials: Optional[Dict[str, Any]] = None
    project_id: Optional[str] = None
    is_public: bool = False
    auto_sync: bool = False
    sync_frequency: Optional[str] = None


class DataSourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    connection_config: Optional[Dict[str, Any]] = None
    credentials: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    auto_sync: Optional[bool] = None
    sync_frequency: Optional[str] = None


class DataSourceResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    type: DataSourceType
    status: DataSourceStatus
    connection_config: Dict[str, Any]
    schema_info: Optional[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]]
    is_public: bool
    auto_sync: bool
    sync_frequency: Optional[str]
    last_sync: Optional[str]
    last_error: Optional[str]
    is_healthy: bool
    created_by_id: str
    project_id: Optional[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class DataSourceListResponse(BaseModel):
    data_sources: List[DataSourceResponse]
    total: int
    page: int
    size: int


class FileUploadResponse(BaseModel):
    filename: str
    file_path: str
    file_size: int
    file_type: str
    columns: Optional[List[str]] = None
    rows: Optional[int] = None
    preview: Optional[List[Dict[str, Any]]] = None


@router.post("/sources", response_model=DataSourceResponse)
async def create_data_source(
    data_source_data: DataSourceCreate,
    current_user: User = Depends(require_permission("manage_data")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new data source"""

    # Validate project access if project_id is provided
    if data_source_data.project_id:
        result = await db.execute(select(Project).where(Project.id == data_source_data.project_id))
        project = result.scalar_one_or_none()

        if not project:
            raise NotFoundError("Project not found")

        if (project.owner_id != current_user.id and
            not project.is_public and
            not current_user.is_admin):
            raise AuthorizationError("Access denied to project")

    new_data_source = DataSource(
        name=data_source_data.name,
        description=data_source_data.description,
        type=data_source_data.type,
        connection_config=data_source_data.connection_config,
        credentials=data_source_data.credentials,  # TODO: Encrypt credentials
        project_id=data_source_data.project_id,
        is_public=data_source_data.is_public,
        auto_sync=data_source_data.auto_sync,
        sync_frequency=data_source_data.sync_frequency,
        created_by_id=current_user.id,
        status=DataSourceStatus.TESTING
    )

    db.add(new_data_source)
    await db.commit()
    await db.refresh(new_data_source)

    return new_data_source


@router.get("/sources", response_model=DataSourceListResponse)
async def list_data_sources(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    type: Optional[DataSourceType] = None,
    status: Optional[DataSourceStatus] = None,
    project_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List data sources"""

    # Build query
    query = select(DataSource)

    # Filter by access permissions
    if not current_user.is_admin:
        query = query.where(
            (DataSource.created_by_id == current_user.id) |
            (DataSource.is_public == True)
        )

    # Apply filters
    if search:
        query = query.where(
            (DataSource.name.ilike(f"%{search}%")) |
            (DataSource.description.ilike(f"%{search}%"))
        )

    if type:
        query = query.where(DataSource.type == type)

    if status:
        query = query.where(DataSource.status == status)

    if project_id:
        query = query.where(DataSource.project_id == project_id)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)

    # Execute query
    result = await db.execute(query)
    data_sources = result.scalars().all()

    return DataSourceListResponse(
        data_sources=data_sources,
        total=total,
        page=page,
        size=size
    )


@router.get("/sources/{data_source_id}", response_model=DataSourceResponse)
async def get_data_source(
    data_source_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get data source by ID"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check access permissions
    if (data_source.created_by_id != current_user.id and
        not data_source.is_public and
        not current_user.is_admin):
        raise AuthorizationError("Access denied")

    return data_source


@router.put("/sources/{data_source_id}", response_model=DataSourceResponse)
async def update_data_source(
    data_source_id: str,
    data_source_update: DataSourceUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update data source"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check ownership
    if data_source.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only data source owner can update")

    # Update fields
    for field, value in data_source_update.dict(exclude_unset=True).items():
        setattr(data_source, field, value)

    await db.commit()
    await db.refresh(data_source)

    return data_source


@router.delete("/sources/{data_source_id}")
async def delete_data_source(
    data_source_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete data source"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check ownership
    if data_source.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only data source owner can delete")

    await db.delete(data_source)
    await db.commit()

    return {"message": "Data source deleted successfully"}


@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    project_id: Optional[str] = Form(None),
    current_user: User = Depends(require_permission("manage_data")),
    db: AsyncSession = Depends(get_db)
):
    """Upload data file"""

    # Validate file type
    allowed_extensions = {'.csv', '.json', '.xlsx', '.parquet', '.txt'}
    file_extension = Path(file.filename).suffix.lower()

    if file_extension not in allowed_extensions:
        raise ValidationError(f"File type {file_extension} not supported")

    # Create upload directory if it doesn't exist
    upload_dir = Path(settings.UPLOAD_DIR)
    upload_dir.mkdir(parents=True, exist_ok=True)

    # Generate unique filename
    import uuid
    unique_filename = f"{uuid.uuid4()}_{file.filename}"
    file_path = upload_dir / unique_filename

    # Save file
    content = await file.read()
    with open(file_path, "wb") as f:
        f.write(content)

    # Analyze file
    file_info = {
        "filename": file.filename,
        "file_path": str(file_path),
        "file_size": len(content),
        "file_type": file_extension
    }

    # Try to read and analyze the file
    try:
        if file_extension == '.csv':
            df = pd.read_csv(file_path, nrows=100)  # Read first 100 rows for preview
            file_info["columns"] = df.columns.tolist()
            file_info["rows"] = len(df)
            file_info["preview"] = df.head(5).to_dict('records')
        elif file_extension == '.json':
            with open(file_path, 'r') as f:
                data = json.load(f)
            if isinstance(data, list) and len(data) > 0:
                file_info["columns"] = list(data[0].keys()) if isinstance(data[0], dict) else None
                file_info["rows"] = len(data)
                file_info["preview"] = data[:5]
        elif file_extension == '.xlsx':
            df = pd.read_excel(file_path, nrows=100)
            file_info["columns"] = df.columns.tolist()
            file_info["rows"] = len(df)
            file_info["preview"] = df.head(5).to_dict('records')
    except Exception as e:
        # If analysis fails, still return basic file info
        file_info["analysis_error"] = str(e)

    return FileUploadResponse(**file_info)


@router.post("/sources/{data_source_id}/test")
async def test_data_source_connection(
    data_source_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Test data source connection"""

    result = await db.execute(select(DataSource).where(DataSource.id == data_source_id))
    data_source = result.scalar_one_or_none()

    if not data_source:
        raise NotFoundError("Data source not found")

    # Check access permissions
    if (data_source.created_by_id != current_user.id and
        not data_source.is_public and
        not current_user.is_admin):
        raise AuthorizationError("Access denied")

    # TODO: Implement actual connection testing based on data source type
    # This is a placeholder implementation

    try:
        # Simulate connection test
        data_source.status = DataSourceStatus.ACTIVE
        data_source.last_error = None
        await db.commit()

        return {
            "status": "success",
            "message": "Connection test successful",
            "connection_string": data_source.get_connection_string()
        }
    except Exception as e:
        data_source.status = DataSourceStatus.ERROR
        data_source.last_error = str(e)
        await db.commit()

        return {
            "status": "error",
            "message": f"Connection test failed: {str(e)}"
        }