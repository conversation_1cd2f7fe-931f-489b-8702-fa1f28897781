from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.ml_model import MLModel, ModelType, ModelStatus, MLFramework
from app.models.project import Project
from app.core.exceptions import NotFoundError, AuthorizationError, ValidationError

router = APIRouter()


# Pydantic models
class MLModelCreate(BaseModel):
    name: str
    description: Optional[str] = None
    version: str = "1.0.0"
    model_type: ModelType
    framework: MLFramework
    algorithm: Optional[str] = None
    training_config: Dict[str, Any]
    hyperparameters: Optional[Dict[str, Any]] = None
    feature_config: Optional[Dict[str, Any]] = None
    project_id: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class MLModelUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    algorithm: Optional[str] = None
    hyperparameters: Optional[Dict[str, Any]] = None
    feature_config: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class MLModelResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    version: str
    model_type: ModelType
    framework: MLFramework
    algorithm: Optional[str]
    status: ModelStatus
    training_config: Dict[str, Any]
    hyperparameters: Optional[Dict[str, Any]]
    feature_config: Optional[Dict[str, Any]]
    model_path: Optional[str]
    model_size_mb: Optional[float]
    model_format: Optional[str]
    metrics: Optional[Dict[str, Any]]
    validation_score: Optional[float]
    test_score: Optional[float]
    training_dataset_id: Optional[str]
    training_samples: Optional[int]
    training_duration_seconds: Optional[int]
    training_start: Optional[str]
    training_end: Optional[str]
    deployment_config: Optional[Dict[str, Any]]
    endpoint_url: Optional[str]
    deployment_status: Optional[str]
    prediction_count: int
    last_prediction: Optional[str]
    drift_score: Optional[float]
    tags: Optional[List[str]]
    metadata: Optional[Dict[str, Any]]
    is_deployed: bool
    is_trainable: bool
    performance_summary: Dict[str, Any]
    created_by_id: str
    project_id: Optional[str]
    pipeline_id: Optional[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class MLModelListResponse(BaseModel):
    models: List[MLModelResponse]
    total: int
    page: int
    size: int


class TrainingRequest(BaseModel):
    dataset_id: str
    training_config: Optional[Dict[str, Any]] = None
    hyperparameters: Optional[Dict[str, Any]] = None


class PredictionRequest(BaseModel):
    data: Dict[str, Any]
    return_probabilities: bool = False


class PredictionResponse(BaseModel):
    prediction: Any
    probabilities: Optional[Dict[str, float]] = None
    model_id: str
    timestamp: str


@router.post("/models", response_model=MLModelResponse)
async def create_ml_model(
    model_data: MLModelCreate,
    current_user: User = Depends(require_permission("train_models")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new ML model"""

    # Validate project access if project_id is provided
    if model_data.project_id:
        result = await db.execute(select(Project).where(Project.id == model_data.project_id))
        project = result.scalar_one_or_none()

        if not project:
            raise NotFoundError("Project not found")

        if (project.owner_id != current_user.id and
            not project.is_public and
            not current_user.is_admin):
            raise AuthorizationError("Access denied to project")

    new_model = MLModel(
        name=model_data.name,
        description=model_data.description,
        version=model_data.version,
        model_type=model_data.model_type,
        framework=model_data.framework,
        algorithm=model_data.algorithm,
        training_config=model_data.training_config,
        hyperparameters=model_data.hyperparameters,
        feature_config=model_data.feature_config,
        project_id=model_data.project_id,
        tags=model_data.tags,
        metadata=model_data.metadata,
        created_by_id=current_user.id,
        status=ModelStatus.TRAINING
    )

    db.add(new_model)
    await db.commit()
    await db.refresh(new_model)

    return new_model


@router.get("/models", response_model=MLModelListResponse)
async def list_ml_models(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    model_type: Optional[ModelType] = None,
    framework: Optional[MLFramework] = None,
    status: Optional[ModelStatus] = None,
    project_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List ML models"""

    # Build query
    query = select(MLModel)

    # Filter by access permissions
    if not current_user.is_admin:
        query = query.where(MLModel.created_by_id == current_user.id)

    # Apply filters
    if search:
        query = query.where(
            (MLModel.name.ilike(f"%{search}%")) |
            (MLModel.description.ilike(f"%{search}%"))
        )

    if model_type:
        query = query.where(MLModel.model_type == model_type)

    if framework:
        query = query.where(MLModel.framework == framework)

    if status:
        query = query.where(MLModel.status == status)

    if project_id:
        query = query.where(MLModel.project_id == project_id)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)

    # Execute query
    result = await db.execute(query)
    models = result.scalars().all()

    return MLModelListResponse(
        models=models,
        total=total,
        page=page,
        size=size
    )


@router.get("/models/{model_id}", response_model=MLModelResponse)
async def get_ml_model(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get ML model by ID"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check access permissions
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Access denied")

    return model


@router.put("/models/{model_id}", response_model=MLModelResponse)
async def update_ml_model(
    model_id: str,
    model_update: MLModelUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can update")

    # Update fields
    for field, value in model_update.dict(exclude_unset=True).items():
        setattr(model, field, value)

    await db.commit()
    await db.refresh(model)

    return model


@router.delete("/models/{model_id}")
async def delete_ml_model(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can delete")

    await db.delete(model)
    await db.commit()

    return {"message": "ML model deleted successfully"}


@router.post("/models/{model_id}/train")
async def train_ml_model(
    model_id: str,
    training_request: TrainingRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Start model training"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can train")

    if not model.is_trainable:
        raise ValidationError("Model cannot be trained in current state")

    # Update model training info
    model.status = ModelStatus.TRAINING
    model.training_dataset_id = training_request.dataset_id
    model.training_start = datetime.utcnow().isoformat()

    if training_request.training_config:
        model.training_config.update(training_request.training_config)

    if training_request.hyperparameters:
        model.hyperparameters = training_request.hyperparameters

    await db.commit()

    # TODO: Queue training job with Celery

    return {
        "message": "Model training started",
        "model_id": model_id,
        "status": "training"
    }


@router.post("/models/{model_id}/predict", response_model=PredictionResponse)
async def predict_with_model(
    model_id: str,
    prediction_request: PredictionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Make prediction with ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check access permissions
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Access denied")

    if not model.is_deployed:
        raise ValidationError("Model is not deployed")

    # TODO: Implement actual prediction logic
    # This would typically involve:
    # 1. Loading the model from storage
    # 2. Preprocessing the input data
    # 3. Making the prediction
    # 4. Postprocessing the results

    # Placeholder prediction
    prediction_result = "placeholder_prediction"
    probabilities = {"class_1": 0.7, "class_2": 0.3} if prediction_request.return_probabilities else None

    # Update model statistics
    model.prediction_count += 1
    model.last_prediction = datetime.utcnow().isoformat()
    await db.commit()

    return PredictionResponse(
        prediction=prediction_result,
        probabilities=probabilities,
        model_id=model_id,
        timestamp=datetime.utcnow().isoformat()
    )


@router.post("/models/{model_id}/deploy")
async def deploy_ml_model(
    model_id: str,
    deployment_config: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Deploy ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can deploy")

    if model.status != ModelStatus.TRAINED:
        raise ValidationError("Only trained models can be deployed")

    # Update model deployment info
    model.status = ModelStatus.DEPLOYED
    model.deployment_config = deployment_config or {}
    model.endpoint_url = f"/api/v1/ml/models/{model_id}/predict"  # Placeholder
    model.deployment_status = "active"

    await db.commit()

    return {
        "message": "Model deployed successfully",
        "model_id": model_id,
        "endpoint_url": model.endpoint_url,
        "status": "deployed"
    }