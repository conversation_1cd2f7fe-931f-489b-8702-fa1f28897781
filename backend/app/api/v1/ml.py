from fastapi import APIRouter, Depends, Query, BackgroundTasks, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
import structlog

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.ml_model import MLModel, ModelType, ModelStatus, MLFramework
from app.models.project import Project
from app.core.exceptions import NotFoundError, AuthorizationError, ValidationError
from app.ml.multicloud_pipeline import ml_pipeline, CloudProvider

router = APIRouter()


# Pydantic models
class MLModelCreate(BaseModel):
    name: str
    description: Optional[str] = None
    version: str = "1.0.0"
    model_type: ModelType
    framework: MLFramework
    algorithm: Optional[str] = None
    training_config: Dict[str, Any]
    hyperparameters: Optional[Dict[str, Any]] = None
    feature_config: Optional[Dict[str, Any]] = None
    project_id: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class MLModelUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    algorithm: Optional[str] = None
    hyperparameters: Optional[Dict[str, Any]] = None
    feature_config: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class MLModelResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    version: str
    model_type: ModelType
    framework: MLFramework
    algorithm: Optional[str]
    status: ModelStatus
    training_config: Dict[str, Any]
    hyperparameters: Optional[Dict[str, Any]]
    feature_config: Optional[Dict[str, Any]]
    model_path: Optional[str]
    model_size_mb: Optional[float]
    model_format: Optional[str]
    metrics: Optional[Dict[str, Any]]
    validation_score: Optional[float]
    test_score: Optional[float]
    training_dataset_id: Optional[str]
    training_samples: Optional[int]
    training_duration_seconds: Optional[int]
    training_start: Optional[str]
    training_end: Optional[str]
    deployment_config: Optional[Dict[str, Any]]
    endpoint_url: Optional[str]
    deployment_status: Optional[str]
    prediction_count: int
    last_prediction: Optional[str]
    drift_score: Optional[float]
    tags: Optional[List[str]]
    metadata: Optional[Dict[str, Any]]
    is_deployed: bool
    is_trainable: bool
    performance_summary: Dict[str, Any]
    created_by_id: str
    project_id: Optional[str]
    pipeline_id: Optional[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class MLModelListResponse(BaseModel):
    models: List[MLModelResponse]
    total: int
    page: int
    size: int


class TrainingRequest(BaseModel):
    dataset_id: str
    training_config: Optional[Dict[str, Any]] = None
    hyperparameters: Optional[Dict[str, Any]] = None


class PredictionRequest(BaseModel):
    data: Dict[str, Any]
    return_probabilities: bool = False


class PredictionResponse(BaseModel):
    prediction: Any
    probabilities: Optional[Dict[str, float]] = None
    model_id: str
    timestamp: str


@router.post("/models", response_model=MLModelResponse)
async def create_ml_model(
    model_data: MLModelCreate,
    current_user: User = Depends(require_permission("train_models")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new ML model"""

    # Validate project access if project_id is provided
    if model_data.project_id:
        result = await db.execute(select(Project).where(Project.id == model_data.project_id))
        project = result.scalar_one_or_none()

        if not project:
            raise NotFoundError("Project not found")

        if (project.owner_id != current_user.id and
            not project.is_public and
            not current_user.is_admin):
            raise AuthorizationError("Access denied to project")

    new_model = MLModel(
        name=model_data.name,
        description=model_data.description,
        version=model_data.version,
        model_type=model_data.model_type,
        framework=model_data.framework,
        algorithm=model_data.algorithm,
        training_config=model_data.training_config,
        hyperparameters=model_data.hyperparameters,
        feature_config=model_data.feature_config,
        project_id=model_data.project_id,
        tags=model_data.tags,
        metadata=model_data.metadata,
        created_by_id=current_user.id,
        status=ModelStatus.TRAINING
    )

    db.add(new_model)
    await db.commit()
    await db.refresh(new_model)

    return new_model


@router.get("/models", response_model=MLModelListResponse)
async def list_ml_models(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    model_type: Optional[ModelType] = None,
    framework: Optional[MLFramework] = None,
    status: Optional[ModelStatus] = None,
    project_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List ML models"""

    # Build query
    query = select(MLModel)

    # Filter by access permissions
    if not current_user.is_admin:
        query = query.where(MLModel.created_by_id == current_user.id)

    # Apply filters
    if search:
        query = query.where(
            (MLModel.name.ilike(f"%{search}%")) |
            (MLModel.description.ilike(f"%{search}%"))
        )

    if model_type:
        query = query.where(MLModel.model_type == model_type)

    if framework:
        query = query.where(MLModel.framework == framework)

    if status:
        query = query.where(MLModel.status == status)

    if project_id:
        query = query.where(MLModel.project_id == project_id)

    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)

    # Execute query
    result = await db.execute(query)
    models = result.scalars().all()

    return MLModelListResponse(
        models=models,
        total=total,
        page=page,
        size=size
    )


@router.get("/models/{model_id}", response_model=MLModelResponse)
async def get_ml_model(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get ML model by ID"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check access permissions
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Access denied")

    return model


@router.put("/models/{model_id}", response_model=MLModelResponse)
async def update_ml_model(
    model_id: str,
    model_update: MLModelUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can update")

    # Update fields
    for field, value in model_update.dict(exclude_unset=True).items():
        setattr(model, field, value)

    await db.commit()
    await db.refresh(model)

    return model


@router.delete("/models/{model_id}")
async def delete_ml_model(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can delete")

    await db.delete(model)
    await db.commit()

    return {"message": "ML model deleted successfully"}


@router.post("/models/{model_id}/train")
async def train_ml_model(
    model_id: str,
    training_request: TrainingRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Start model training"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can train")

    if not model.is_trainable:
        raise ValidationError("Model cannot be trained in current state")

    # Update model training info
    model.status = ModelStatus.TRAINING
    model.training_dataset_id = training_request.dataset_id
    model.training_start = datetime.utcnow().isoformat()

    if training_request.training_config:
        model.training_config.update(training_request.training_config)

    if training_request.hyperparameters:
        model.hyperparameters = training_request.hyperparameters

    await db.commit()

    # TODO: Queue training job with Celery

    return {
        "message": "Model training started",
        "model_id": model_id,
        "status": "training"
    }


@router.post("/models/{model_id}/predict", response_model=PredictionResponse)
async def predict_with_model(
    model_id: str,
    prediction_request: PredictionRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Make prediction with ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check access permissions
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Access denied")

    if not model.is_deployed:
        raise ValidationError("Model is not deployed")

    # TODO: Implement actual prediction logic
    # This would typically involve:
    # 1. Loading the model from storage
    # 2. Preprocessing the input data
    # 3. Making the prediction
    # 4. Postprocessing the results

    # Placeholder prediction
    prediction_result = "placeholder_prediction"
    probabilities = {"class_1": 0.7, "class_2": 0.3} if prediction_request.return_probabilities else None

    # Update model statistics
    model.prediction_count += 1
    model.last_prediction = datetime.utcnow().isoformat()
    await db.commit()

    return PredictionResponse(
        prediction=prediction_result,
        probabilities=probabilities,
        model_id=model_id,
        timestamp=datetime.utcnow().isoformat()
    )


@router.post("/models/{model_id}/deploy")
async def deploy_ml_model(
    model_id: str,
    deployment_config: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Deploy ML model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise NotFoundError("ML model not found")

    # Check ownership
    if model.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only model owner can deploy")

    if model.status != ModelStatus.TRAINED:
        raise ValidationError("Only trained models can be deployed")

    # Update model deployment info
    model.status = ModelStatus.DEPLOYED
    model.deployment_config = deployment_config or {}
    model.endpoint_url = f"/api/v1/ml/models/{model_id}/predict"  # Placeholder
    model.deployment_status = "active"

    await db.commit()

    return {
        "message": "Model deployed successfully",
        "model_id": model_id,
        "endpoint_url": model.endpoint_url,
        "status": "deployed"
    }


# Enhanced ML Training Endpoints
logger = structlog.get_logger()

class AutoMLRequest(BaseModel):
    name: str
    dataset_id: str
    target_column: str
    model_type: ModelType
    cloud_provider: Optional[CloudProvider] = CloudProvider.MULTI_CLOUD
    time_budget_hours: int = 2
    quality_target: str = "medium"
    project_id: Optional[str] = None


class TrainingJobResponse(BaseModel):
    job_id: str
    model_id: str
    status: str
    cloud_provider: str
    estimated_completion: str


@router.post("/automl/train", response_model=TrainingJobResponse)
async def start_automl_training(
    request: AutoMLRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_permission("train_models")),
    db: AsyncSession = Depends(get_db)
):
    """Start AutoML training across multiple cloud providers"""

    try:
        # Validate project access
        if request.project_id:
            result = await db.execute(select(Project).where(Project.id == request.project_id))
            project = result.scalar_one_or_none()

            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Project not found"
                )

            if (project.owner_id != current_user.id and
                not project.is_public and
                not current_user.is_admin):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to project"
                )

        # Create ML model record
        new_model = MLModel(
            name=request.name,
            description=f"AutoML model for {request.target_column}",
            version="1.0.0",
            model_type=request.model_type,
            framework=MLFramework.AUTOGLUON,
            algorithm="automl",
            training_config={
                "dataset_id": request.dataset_id,
                "target_column": request.target_column,
                "time_budget_hours": request.time_budget_hours,
                "quality_target": request.quality_target,
                "cloud_provider": request.cloud_provider.value
            },
            project_id=request.project_id,
            created_by_id=current_user.id,
            status=ModelStatus.TRAINING
        )

        db.add(new_model)
        await db.commit()
        await db.refresh(new_model)

        # Start AutoML training in background
        experiment_id = await ml_pipeline.create_automl_experiment(
            name=request.name,
            dataset_id=request.dataset_id,
            model_type=request.model_type,
            target_column=request.target_column,
            cloud_provider=request.cloud_provider,
            time_budget_hours=request.time_budget_hours,
            quality_target=request.quality_target
        )

        # Update model with experiment ID
        new_model.metadata = {"experiment_id": experiment_id}
        await db.commit()

        # Start background monitoring task
        background_tasks.add_task(
            monitor_training_job,
            new_model.id,
            experiment_id
        )

        logger.info(
            "AutoML training started",
            model_id=new_model.id,
            experiment_id=experiment_id,
            user_id=current_user.id
        )

        return TrainingJobResponse(
            job_id=experiment_id,
            model_id=new_model.id,
            status="training",
            cloud_provider=request.cloud_provider.value,
            estimated_completion=(datetime.utcnow() + timedelta(hours=request.time_budget_hours)).isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("AutoML training failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start AutoML training"
        )


@router.get("/models/{model_id}/training-status")
async def get_training_status(
    model_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get training status for a model"""

    result = await db.execute(select(MLModel).where(MLModel.id == model_id))
    model = result.scalar_one_or_none()

    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )

    # Check access permissions
    if (model.created_by_id != current_user.id and
        not current_user.is_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    # Get experiment status from ML pipeline
    experiment_id = model.metadata.get("experiment_id") if model.metadata else None

    if experiment_id and experiment_id in ml_pipeline.experiments:
        experiment = ml_pipeline.experiments[experiment_id]

        return {
            "model_id": model_id,
            "status": experiment.status,
            "progress": calculate_training_progress(experiment),
            "metrics": experiment.metrics,
            "created_at": experiment.created_at.isoformat(),
            "completed_at": experiment.completed_at.isoformat() if experiment.completed_at else None,
            "cloud_provider": experiment.cloud_provider.value,
            "estimated_completion": estimate_completion_time(experiment)
        }
    else:
        return {
            "model_id": model_id,
            "status": model.status.value,
            "progress": 0 if model.status == ModelStatus.TRAINING else 100,
            "metrics": model.metrics or {},
            "created_at": model.created_at.isoformat(),
            "completed_at": model.updated_at.isoformat() if model.status != ModelStatus.TRAINING else None
        }


async def monitor_training_job(model_id: str, experiment_id: str):
    """Background task to monitor training job progress"""

    import asyncio

    while True:
        try:
            # Check if experiment is complete
            if experiment_id in ml_pipeline.experiments:
                experiment = ml_pipeline.experiments[experiment_id]

                if experiment.status == "completed":
                    # Update model status and metrics
                    async for session in get_db():
                        result = await session.execute(select(MLModel).where(MLModel.id == model_id))
                        model = result.scalar_one_or_none()

                        if model:
                            model.status = ModelStatus.TRAINED
                            model.metrics = experiment.metrics
                            model.model_artifacts = experiment.model_artifacts
                            await session.commit()

                            logger.info(
                                "Training completed",
                                model_id=model_id,
                                experiment_id=experiment_id,
                                metrics=experiment.metrics
                            )
                        break
                    break

                elif experiment.status == "failed":
                    # Update model status to failed
                    async for session in get_db():
                        result = await session.execute(select(MLModel).where(MLModel.id == model_id))
                        model = result.scalar_one_or_none()

                        if model:
                            model.status = ModelStatus.FAILED
                            await session.commit()

                            logger.error(
                                "Training failed",
                                model_id=model_id,
                                experiment_id=experiment_id
                            )
                        break
                    break

            # Wait before checking again
            await asyncio.sleep(60)  # Check every minute

        except Exception as e:
            logger.error("Error monitoring training job", error=str(e))
            await asyncio.sleep(60)


def calculate_training_progress(experiment) -> int:
    """Calculate training progress percentage"""

    if experiment.status == "completed":
        return 100
    elif experiment.status == "failed":
        return 0
    elif experiment.status == "running":
        # Estimate progress based on time elapsed
        if experiment.created_at:
            elapsed = datetime.utcnow() - experiment.created_at
            total_budget = timedelta(hours=experiment.hyperparameters.get("time_budget_hours", 2))
            progress = min(int((elapsed / total_budget) * 100), 95)  # Cap at 95% until complete
            return progress

    return 0


def estimate_completion_time(experiment) -> Optional[str]:
    """Estimate completion time for training"""

    if experiment.status in ["completed", "failed"]:
        return None

    if experiment.created_at:
        time_budget = experiment.hyperparameters.get("time_budget_hours", 2)
        estimated_completion = experiment.created_at + timedelta(hours=time_budget)
        return estimated_completion.isoformat()

    return None