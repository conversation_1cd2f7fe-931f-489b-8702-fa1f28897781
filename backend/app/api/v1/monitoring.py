from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import PlainTextResponse
from typing import Dict, Any
import structlog

from app.core.monitoring import health_checker, metrics_collector, alert_manager
from app.core.auth import get_current_user
from app.models.user import User

logger = structlog.get_logger()

router = APIRouter(prefix="/monitoring", tags=["monitoring"])

@router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    try:
        health_data = await health_checker.comprehensive_health_check()
        
        if health_data["status"] == "healthy":
            return health_data
        else:
            raise HTTPException(status_code=503, detail=health_data)
            
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": health_data.get("timestamp")
            }
        )

@router.get("/health/database")
async def database_health():
    """Database-specific health check"""
    return await health_checker.check_database_health()

@router.get("/health/redis")
async def redis_health():
    """Redis-specific health check"""
    return health_checker.check_redis_health()

@router.get("/health/system")
async def system_health():
    """System resources health check"""
    return health_checker.check_system_health()

@router.get("/health/celery")
async def celery_health():
    """Celery workers health check"""
    return await health_checker.check_celery_health()

@router.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    metrics_data = metrics_collector.get_metrics()
    return PlainTextResponse(
        content=metrics_data,
        media_type="text/plain; version=0.0.4; charset=utf-8"
    )

@router.get("/alerts")
async def get_alerts(current_user: User = Depends(get_current_user)):
    """Get current alerts (requires authentication)"""
    if current_user.role not in ["admin", "data_engineer"]:
        raise HTTPException(status_code=403, detail="Insufficient permissions")
    
    health_data = await health_checker.comprehensive_health_check()
    alerts = await alert_manager.check_alerts(health_data)
    
    return {
        "alerts": alerts,
        "total_alerts": len(alerts),
        "critical_alerts": len([a for a in alerts if a.get("severity") == "critical"]),
        "warning_alerts": len([a for a in alerts if a.get("severity") == "warning"]),
        "timestamp": health_data["timestamp"]
    }

@router.get("/status")
async def get_status():
    """Get overall platform status"""
    health_data = await health_checker.comprehensive_health_check()
    alerts = await alert_manager.check_alerts(health_data)
    
    # Calculate uptime (simplified - in production, store startup time)
    import time
    uptime_seconds = time.time() - getattr(get_status, '_start_time', time.time())
    setattr(get_status, '_start_time', getattr(get_status, '_start_time', time.time()))
    
    return {
        "status": health_data["status"],
        "uptime_seconds": uptime_seconds,
        "version": "1.0.0",
        "environment": "production",
        "services": {
            service: data["status"] 
            for service, data in health_data.get("checks", {}).items()
        },
        "alerts_count": len(alerts),
        "last_check": health_data["timestamp"]
    }
