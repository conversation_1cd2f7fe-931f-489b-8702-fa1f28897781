from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.project import Project, ProjectType, ProjectStatus
from app.core.exceptions import NotFoundError, AuthorizationError

router = APIRouter()


# Pydantic models
class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    type: ProjectType = ProjectType.DATA_SCIENCE
    is_public: bool = False
    tags: Optional[List[str]] = None
    settings: Optional[Dict[str, Any]] = None


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[ProjectType] = None
    is_public: Optional[bool] = None
    tags: Optional[List[str]] = None
    settings: Optional[Dict[str, Any]] = None


class ProjectResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    type: ProjectType
    status: ProjectStatus
    is_public: bool
    is_template: bool
    tags: Optional[List[str]]
    data_sources_count: int
    pipelines_count: int
    models_count: int
    workflows_count: int
    total_assets: int
    owner_id: str
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    projects: List[ProjectResponse]
    total: int
    page: int
    size: int


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(require_permission("create_projects")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new project"""
    
    new_project = Project(
        name=project_data.name,
        description=project_data.description,
        type=project_data.type,
        is_public=project_data.is_public,
        tags=project_data.tags,
        settings=project_data.settings,
        owner_id=current_user.id
    )
    
    db.add(new_project)
    await db.commit()
    await db.refresh(new_project)
    
    return new_project


@router.get("/", response_model=ProjectListResponse)
async def list_projects(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    type: Optional[ProjectType] = None,
    status: Optional[ProjectStatus] = None,
    is_public: Optional[bool] = None,
    my_projects: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List projects"""
    
    # Build query
    query = select(Project)
    
    # Filter by ownership or public projects
    if my_projects:
        query = query.where(Project.owner_id == current_user.id)
    elif not current_user.is_admin:
        # Non-admin users can only see their own projects and public projects
        query = query.where(
            (Project.owner_id == current_user.id) | 
            (Project.is_public == True)
        )
    
    # Apply filters
    if search:
        query = query.where(
            (Project.name.ilike(f"%{search}%")) |
            (Project.description.ilike(f"%{search}%"))
        )
    
    if type:
        query = query.where(Project.type == type)
    
    if status:
        query = query.where(Project.status == status)
    
    if is_public is not None:
        query = query.where(Project.is_public == is_public)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)
    
    # Execute query
    result = await db.execute(query)
    projects = result.scalars().all()
    
    return ProjectListResponse(
        projects=projects,
        total=total,
        page=page,
        size=size
    )


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get project by ID"""
    
    result = await db.execute(select(Project).where(Project.id == project_id))
    project = result.scalar_one_or_none()
    
    if not project:
        raise NotFoundError("Project not found")
    
    # Check access permissions
    if (project.owner_id != current_user.id and 
        not project.is_public and 
        not current_user.is_admin):
        raise AuthorizationError("Access denied")
    
    return project


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: str,
    project_update: ProjectUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update project"""
    
    result = await db.execute(select(Project).where(Project.id == project_id))
    project = result.scalar_one_or_none()
    
    if not project:
        raise NotFoundError("Project not found")
    
    # Check ownership
    if project.owner_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only project owner can update project")
    
    # Update project fields
    for field, value in project_update.dict(exclude_unset=True).items():
        setattr(project, field, value)
    
    await db.commit()
    await db.refresh(project)
    
    return project


@router.delete("/{project_id}")
async def delete_project(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete project (soft delete)"""
    
    result = await db.execute(select(Project).where(Project.id == project_id))
    project = result.scalar_one_or_none()
    
    if not project:
        raise NotFoundError("Project not found")
    
    # Check ownership
    if project.owner_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only project owner can delete project")
    
    # Soft delete
    project.status = ProjectStatus.DELETED
    await db.commit()
    
    return {"message": "Project deleted successfully"}


@router.post("/{project_id}/archive")
async def archive_project(
    project_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Archive project"""
    
    result = await db.execute(select(Project).where(Project.id == project_id))
    project = result.scalar_one_or_none()
    
    if not project:
        raise NotFoundError("Project not found")
    
    # Check ownership
    if project.owner_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only project owner can archive project")
    
    project.status = ProjectStatus.ARCHIVED
    await db.commit()
    
    return {"message": "Project archived successfully"}
