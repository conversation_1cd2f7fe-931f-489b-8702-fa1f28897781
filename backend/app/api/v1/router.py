from fastapi import APIRouter

from app.api.v1 import auth, data, ml, workflows, users, projects, monitoring, compliance

api_router = APIRouter()

# Include all route modules
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(projects.router, prefix="/projects", tags=["Projects"])
api_router.include_router(data.router, prefix="/data", tags=["Data Management"])
api_router.include_router(workflows.router, prefix="/workflows", tags=["Workflows"])
api_router.include_router(ml.router, prefix="/ml", tags=["Machine Learning"])
api_router.include_router(compliance.router, prefix="/compliance", tags=["Compliance & GDPR"])
api_router.include_router(monitoring.router, tags=["Monitoring"])
