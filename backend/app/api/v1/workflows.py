from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.models.user import User
from app.models.workflow import Workflow, WorkflowType, WorkflowStatus
from app.models.project import Project
from app.core.exceptions import NotFoundError, AuthorizationError, ValidationError

router = APIRouter()


# Pydantic models
class WorkflowCreate(BaseModel):
    name: str
    description: Optional[str] = None
    type: WorkflowType
    definition: Dict[str, Any]
    nodes: List[Dict[str, Any]] = []
    edges: List[Dict[str, Any]] = []
    config: Optional[Dict[str, Any]] = None
    variables: Optional[Dict[str, Any]] = None
    project_id: Optional[str] = None
    is_public: bool = False
    is_template: bool = False
    tags: Optional[List[str]] = None


class WorkflowUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    definition: Optional[Dict[str, Any]] = None
    nodes: Optional[List[Dict[str, Any]]] = None
    edges: Optional[List[Dict[str, Any]]] = None
    config: Optional[Dict[str, Any]] = None
    variables: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    is_template: Optional[bool] = None
    tags: Optional[List[str]] = None


class WorkflowResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    type: WorkflowType
    status: WorkflowStatus
    definition: Dict[str, Any]
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]
    config: Optional[Dict[str, Any]]
    variables: Optional[Dict[str, Any]]
    is_scheduled: bool
    schedule_expression: Optional[str]
    timezone: str
    max_retries: int
    timeout_minutes: Optional[int]
    parallel_execution: bool
    last_execution_id: Optional[str]
    last_execution_status: Optional[str]
    last_execution_start: Optional[str]
    last_execution_end: Optional[str]
    total_executions: int
    successful_executions: int
    failed_executions: int
    success_rate: float
    avg_duration_seconds: Optional[float]
    is_public: bool
    is_template: bool
    tags: Optional[List[str]]
    node_count: int
    edge_count: int
    created_by_id: str
    project_id: Optional[str]
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class WorkflowListResponse(BaseModel):
    workflows: List[WorkflowResponse]
    total: int
    page: int
    size: int


@router.post("/", response_model=WorkflowResponse)
async def create_workflow(
    workflow_data: WorkflowCreate,
    current_user: User = Depends(require_permission("manage_data")),
    db: AsyncSession = Depends(get_db)
):
    """Create a new workflow"""
    
    # Validate project access if project_id is provided
    if workflow_data.project_id:
        result = await db.execute(select(Project).where(Project.id == workflow_data.project_id))
        project = result.scalar_one_or_none()
        
        if not project:
            raise NotFoundError("Project not found")
        
        if (project.owner_id != current_user.id and 
            not project.is_public and 
            not current_user.is_admin):
            raise AuthorizationError("Access denied to project")
    
    new_workflow = Workflow(
        name=workflow_data.name,
        description=workflow_data.description,
        type=workflow_data.type,
        definition=workflow_data.definition,
        nodes=workflow_data.nodes,
        edges=workflow_data.edges,
        config=workflow_data.config,
        variables=workflow_data.variables,
        project_id=workflow_data.project_id,
        is_public=workflow_data.is_public,
        is_template=workflow_data.is_template,
        tags=workflow_data.tags,
        created_by_id=current_user.id
    )
    
    db.add(new_workflow)
    await db.commit()
    await db.refresh(new_workflow)
    
    return new_workflow


@router.get("/", response_model=WorkflowListResponse)
async def list_workflows(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    type: Optional[WorkflowType] = None,
    status: Optional[WorkflowStatus] = None,
    project_id: Optional[str] = None,
    is_template: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """List workflows"""
    
    # Build query
    query = select(Workflow)
    
    # Filter by access permissions
    if not current_user.is_admin:
        query = query.where(
            (Workflow.created_by_id == current_user.id) | 
            (Workflow.is_public == True)
        )
    
    # Apply filters
    if search:
        query = query.where(
            (Workflow.name.ilike(f"%{search}%")) |
            (Workflow.description.ilike(f"%{search}%"))
        )
    
    if type:
        query = query.where(Workflow.type == type)
    
    if status:
        query = query.where(Workflow.status == status)
    
    if project_id:
        query = query.where(Workflow.project_id == project_id)
    
    if is_template is not None:
        query = query.where(Workflow.is_template == is_template)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar()
    
    # Apply pagination
    query = query.offset((page - 1) * size).limit(size)
    
    # Execute query
    result = await db.execute(query)
    workflows = result.scalars().all()
    
    return WorkflowListResponse(
        workflows=workflows,
        total=total,
        page=page,
        size=size
    )


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get workflow by ID"""
    
    result = await db.execute(select(Workflow).where(Workflow.id == workflow_id))
    workflow = result.scalar_one_or_none()
    
    if not workflow:
        raise NotFoundError("Workflow not found")
    
    # Check access permissions
    if (workflow.created_by_id != current_user.id and 
        not workflow.is_public and 
        not current_user.is_admin):
        raise AuthorizationError("Access denied")
    
    return workflow


@router.put("/{workflow_id}", response_model=WorkflowResponse)
async def update_workflow(
    workflow_id: str,
    workflow_update: WorkflowUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update workflow"""
    
    result = await db.execute(select(Workflow).where(Workflow.id == workflow_id))
    workflow = result.scalar_one_or_none()
    
    if not workflow:
        raise NotFoundError("Workflow not found")
    
    # Check ownership
    if workflow.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only workflow owner can update")
    
    # Update fields
    for field, value in workflow_update.dict(exclude_unset=True).items():
        setattr(workflow, field, value)
    
    await db.commit()
    await db.refresh(workflow)
    
    return workflow


@router.delete("/{workflow_id}")
async def delete_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete workflow"""
    
    result = await db.execute(select(Workflow).where(Workflow.id == workflow_id))
    workflow = result.scalar_one_or_none()
    
    if not workflow:
        raise NotFoundError("Workflow not found")
    
    # Check ownership
    if workflow.created_by_id != current_user.id and not current_user.is_admin:
        raise AuthorizationError("Only workflow owner can delete")
    
    await db.delete(workflow)
    await db.commit()
    
    return {"message": "Workflow deleted successfully"}


@router.post("/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Execute workflow"""
    
    result = await db.execute(select(Workflow).where(Workflow.id == workflow_id))
    workflow = result.scalar_one_or_none()
    
    if not workflow:
        raise NotFoundError("Workflow not found")
    
    # Check access permissions
    if (workflow.created_by_id != current_user.id and 
        not workflow.is_public and 
        not current_user.is_admin):
        raise AuthorizationError("Access denied")
    
    if not workflow.can_execute:
        raise ValidationError("Workflow cannot be executed in current state")
    
    # TODO: Implement actual workflow execution
    # This would typically involve:
    # 1. Creating a workflow execution record
    # 2. Queuing the workflow for execution via Celery
    # 3. Returning execution ID
    
    import uuid
    execution_id = str(uuid.uuid4())
    
    # Update workflow execution info
    workflow.last_execution_id = execution_id
    workflow.last_execution_status = "running"
    workflow.last_execution_start = datetime.utcnow().isoformat()
    workflow.total_executions += 1
    
    await db.commit()
    
    return {
        "execution_id": execution_id,
        "status": "started",
        "message": "Workflow execution started"
    }
