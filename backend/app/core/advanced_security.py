"""
Advanced Security Framework
Implements enterprise-grade security features including MFA, SSO, threat detection
"""

import asyncio
import hashlib
import hmac
import json
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import pyotp
import qrcode
from io import BytesIO
import base64
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import jwt
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import structlog
from geoip2 import database as geoip_database
from user_agents import parse as parse_user_agent

from app.core.config import settings
from app.core.compliance import soc2_compliance, encryption_manager

logger = structlog.get_logger()

class ThreatLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AuthenticationMethod(Enum):
    PASSWORD = "password"
    MFA_TOTP = "mfa_totp"
    MFA_SMS = "mfa_sms"
    SSO_SAML = "sso_saml"
    SSO_OAUTH = "sso_oauth"
    CERTIFICATE = "certificate"
    BIOMETRIC = "biometric"

class SecurityEvent(Enum):
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    MFA_SUCCESS = "mfa_success"
    MFA_FAILURE = "mfa_failure"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    ACCOUNT_LOCKOUT = "account_lockout"
    PASSWORD_CHANGE = "password_change"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_ACCESS = "data_access"
    THREAT_DETECTED = "threat_detected"

class AdvancedAuthenticationManager:
    """Advanced authentication with MFA, SSO, and threat detection"""
    
    def __init__(self):
        self.failed_attempts: Dict[str, List[datetime]] = {}
        self.suspicious_ips: Dict[str, ThreatLevel] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.geoip_reader = None
        self._load_geoip_database()
    
    def _load_geoip_database(self):
        """Load GeoIP database for location-based security"""
        try:
            self.geoip_reader = geoip_database.Reader('/usr/share/GeoIP/GeoLite2-City.mmdb')
        except Exception as e:
            logger.warning("GeoIP database not available", error=str(e))
    
    def generate_mfa_secret(self, user_id: str, email: str) -> Tuple[str, str]:
        """Generate TOTP secret and QR code for MFA setup"""
        
        secret = pyotp.random_base32()
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=email,
            issuer_name="AI Data Platform"
        )
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        logger.info("MFA secret generated", user_id=user_id)
        
        return secret, qr_code_base64
    
    def verify_mfa_token(self, secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token with time window tolerance"""
        
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=window)
    
    def analyze_login_risk(self, request: Request, user_id: str) -> ThreatLevel:
        """Analyze login attempt for risk factors"""
        
        risk_factors = []
        risk_score = 0
        
        # Get client information
        ip_address = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # Check IP reputation
        if ip_address in self.suspicious_ips:
            risk_score += 30
            risk_factors.append("suspicious_ip")
        
        # Check for unusual location
        location_risk = self._check_location_risk(ip_address, user_id)
        risk_score += location_risk
        if location_risk > 20:
            risk_factors.append("unusual_location")
        
        # Check device fingerprint
        device_risk = self._check_device_risk(user_agent, user_id)
        risk_score += device_risk
        if device_risk > 15:
            risk_factors.append("new_device")
        
        # Check time-based patterns
        time_risk = self._check_time_patterns(user_id)
        risk_score += time_risk
        if time_risk > 10:
            risk_factors.append("unusual_time")
        
        # Check failed attempts
        failed_attempts = self._get_recent_failed_attempts(ip_address)
        if failed_attempts > 3:
            risk_score += 25
            risk_factors.append("multiple_failures")
        
        # Determine threat level
        if risk_score >= 70:
            threat_level = ThreatLevel.CRITICAL
        elif risk_score >= 50:
            threat_level = ThreatLevel.HIGH
        elif risk_score >= 30:
            threat_level = ThreatLevel.MEDIUM
        else:
            threat_level = ThreatLevel.LOW
        
        logger.info(
            "Login risk analysis",
            user_id=user_id,
            ip_address=ip_address,
            risk_score=risk_score,
            threat_level=threat_level.value,
            risk_factors=risk_factors
        )
        
        return threat_level
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request"""
        
        # Check for forwarded headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _check_location_risk(self, ip_address: str, user_id: str) -> int:
        """Check if login location is unusual for user"""
        
        if not self.geoip_reader:
            return 0
        
        try:
            response = self.geoip_reader.city(ip_address)
            country = response.country.iso_code
            city = response.city.name
            
            # In production, compare with user's historical locations
            # For now, return low risk for known safe countries
            safe_countries = ["US", "CA", "GB", "DE", "FR", "AU", "JP"]
            if country in safe_countries:
                return 5
            else:
                return 25
                
        except Exception:
            return 15  # Unknown location gets medium risk
    
    def _check_device_risk(self, user_agent: str, user_id: str) -> int:
        """Check if device/browser is new for user"""
        
        try:
            parsed_ua = parse_user_agent(user_agent)
            device_fingerprint = f"{parsed_ua.browser.family}_{parsed_ua.os.family}"
            
            # In production, compare with user's known devices
            # For now, return low risk for common browsers
            common_browsers = ["Chrome", "Firefox", "Safari", "Edge"]
            if parsed_ua.browser.family in common_browsers:
                return 5
            else:
                return 20
                
        except Exception:
            return 15
    
    def _check_time_patterns(self, user_id: str) -> int:
        """Check if login time is unusual for user"""
        
        current_hour = datetime.utcnow().hour
        
        # Business hours (9 AM - 6 PM UTC) are lower risk
        if 9 <= current_hour <= 18:
            return 0
        # Evening hours are medium risk
        elif 18 < current_hour <= 23 or 6 <= current_hour < 9:
            return 5
        # Night hours are higher risk
        else:
            return 15
    
    def _get_recent_failed_attempts(self, ip_address: str) -> int:
        """Get number of recent failed attempts from IP"""
        
        if ip_address not in self.failed_attempts:
            return 0
        
        # Count attempts in last hour
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        recent_attempts = [
            attempt for attempt in self.failed_attempts[ip_address]
            if attempt > cutoff_time
        ]
        
        return len(recent_attempts)
    
    def record_failed_attempt(self, ip_address: str):
        """Record failed login attempt"""
        
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []
        
        self.failed_attempts[ip_address].append(datetime.utcnow())
        
        # Clean old attempts (older than 24 hours)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.failed_attempts[ip_address] = [
            attempt for attempt in self.failed_attempts[ip_address]
            if attempt > cutoff_time
        ]
        
        # Mark IP as suspicious after multiple failures
        if len(self.failed_attempts[ip_address]) >= 5:
            self.suspicious_ips[ip_address] = ThreatLevel.HIGH
    
    def create_secure_session(
        self,
        user_id: str,
        ip_address: str,
        user_agent: str,
        authentication_methods: List[AuthenticationMethod]
    ) -> str:
        """Create secure session with enhanced tracking"""
        
        session_id = secrets.token_urlsafe(32)
        session_data = {
            "user_id": user_id,
            "session_id": session_id,
            "created_at": datetime.utcnow(),
            "last_activity": datetime.utcnow(),
            "ip_address": ip_address,
            "user_agent": user_agent,
            "authentication_methods": [method.value for method in authentication_methods],
            "is_active": True,
            "security_level": "high" if AuthenticationMethod.MFA_TOTP in authentication_methods else "standard"
        }
        
        self.active_sessions[session_id] = session_data
        
        logger.info(
            "Secure session created",
            user_id=user_id,
            session_id=session_id,
            security_level=session_data["security_level"]
        )
        
        return session_id
    
    def validate_session(self, session_id: str, ip_address: str) -> Optional[Dict[str, Any]]:
        """Validate session with security checks"""
        
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        
        # Check if session is active
        if not session["is_active"]:
            return None
        
        # Check session timeout (8 hours for standard, 12 hours for high security)
        timeout_hours = 12 if session["security_level"] == "high" else 8
        if datetime.utcnow() - session["last_activity"] > timedelta(hours=timeout_hours):
            self.invalidate_session(session_id)
            return None
        
        # Check IP address consistency
        if session["ip_address"] != ip_address:
            logger.warning(
                "Session IP mismatch",
                session_id=session_id,
                original_ip=session["ip_address"],
                current_ip=ip_address
            )
            # In high-security mode, invalidate session on IP change
            if session["security_level"] == "high":
                self.invalidate_session(session_id)
                return None
        
        # Update last activity
        session["last_activity"] = datetime.utcnow()
        
        return session
    
    def invalidate_session(self, session_id: str):
        """Invalidate session"""
        
        if session_id in self.active_sessions:
            self.active_sessions[session_id]["is_active"] = False
            logger.info("Session invalidated", session_id=session_id)

class ThreatDetectionEngine:
    """Real-time threat detection and response"""
    
    def __init__(self):
        self.threat_patterns = {
            "sql_injection": [
                r"(\%27)|(\')|(\-\-)|(\%23)|(#)",
                r"((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))",
                r"\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))"
            ],
            "xss": [
                r"<script[^>]*>.*?</script>",
                r"javascript:",
                r"on\w+\s*="
            ],
            "path_traversal": [
                r"\.\.\/",
                r"\.\.\\",
                r"\%2e\%2e\%2f",
                r"\%2e\%2e\%5c"
            ]
        }
        self.anomaly_thresholds = {
            "request_rate": 100,  # requests per minute
            "error_rate": 0.1,    # 10% error rate
            "data_volume": 100 * 1024 * 1024  # 100MB per request
        }
    
    def analyze_request(self, request: Request) -> ThreatLevel:
        """Analyze incoming request for threats"""
        
        threat_level = ThreatLevel.LOW
        detected_threats = []
        
        # Check URL and parameters for injection attacks
        url_threats = self._check_injection_patterns(str(request.url))
        if url_threats:
            detected_threats.extend(url_threats)
            threat_level = ThreatLevel.HIGH
        
        # Check headers for suspicious patterns
        header_threats = self._check_headers(request.headers)
        if header_threats:
            detected_threats.extend(header_threats)
            threat_level = max(threat_level, ThreatLevel.MEDIUM)
        
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.anomaly_thresholds["data_volume"]:
            detected_threats.append("large_payload")
            threat_level = max(threat_level, ThreatLevel.MEDIUM)
        
        if detected_threats:
            logger.warning(
                "Threats detected in request",
                url=str(request.url),
                threats=detected_threats,
                threat_level=threat_level.value
            )
        
        return threat_level
    
    def _check_injection_patterns(self, text: str) -> List[str]:
        """Check text for injection attack patterns"""
        
        detected = []
        text_lower = text.lower()
        
        for threat_type, patterns in self.threat_patterns.items():
            for pattern in patterns:
                import re
                if re.search(pattern, text_lower, re.IGNORECASE):
                    detected.append(threat_type)
                    break
        
        return detected
    
    def _check_headers(self, headers: Dict[str, str]) -> List[str]:
        """Check headers for suspicious patterns"""
        
        detected = []
        
        # Check for suspicious user agents
        user_agent = headers.get("user-agent", "").lower()
        suspicious_agents = ["sqlmap", "nikto", "nmap", "masscan", "zap"]
        if any(agent in user_agent for agent in suspicious_agents):
            detected.append("suspicious_user_agent")
        
        # Check for unusual headers
        unusual_headers = ["x-forwarded-host", "x-cluster-client-ip", "x-real-ip"]
        for header in unusual_headers:
            if header in headers:
                detected.append("unusual_header")
                break
        
        return detected

class ZeroTrustNetworkAccess:
    """Zero Trust Network Access implementation"""
    
    def __init__(self):
        self.device_certificates: Dict[str, Dict[str, Any]] = {}
        self.network_policies: Dict[str, List[str]] = {}
    
    def verify_device_certificate(self, certificate_data: str) -> bool:
        """Verify device certificate for Zero Trust access"""
        
        try:
            # In production, this would verify against a CA
            # For now, we'll do basic validation
            
            cert_hash = hashlib.sha256(certificate_data.encode()).hexdigest()
            
            if cert_hash in self.device_certificates:
                cert_info = self.device_certificates[cert_hash]
                
                # Check if certificate is still valid
                if cert_info["expires_at"] > datetime.utcnow():
                    return True
            
            return False
            
        except Exception as e:
            logger.error("Certificate verification failed", error=str(e))
            return False
    
    def evaluate_network_policy(
        self,
        user_id: str,
        resource: str,
        action: str,
        context: Dict[str, Any]
    ) -> bool:
        """Evaluate Zero Trust network policy"""
        
        # Check user permissions
        user_policies = self.network_policies.get(user_id, [])
        required_permission = f"{resource}:{action}"
        
        if required_permission not in user_policies:
            logger.warning(
                "Zero Trust policy violation",
                user_id=user_id,
                resource=resource,
                action=action,
                required_permission=required_permission
            )
            return False
        
        # Check contextual factors
        if context.get("threat_level") == ThreatLevel.CRITICAL:
            return False
        
        if context.get("location_risk", 0) > 50:
            return False
        
        return True

# Initialize security components
auth_manager = AdvancedAuthenticationManager()
threat_detector = ThreatDetectionEngine()
zero_trust = ZeroTrustNetworkAccess()
