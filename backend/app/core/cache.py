import json
import pickle
import hashlib
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import redis
import structlog
from functools import wraps
import asyncio

from app.core.config import settings

logger = structlog.get_logger()

class CacheManager:
    """Redis-based caching system with multiple strategies"""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=False)
        self.default_ttl = 3600  # 1 hour
        
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set cache value with optional TTL"""
        try:
            ttl = ttl or self.default_ttl
            serialized_value = pickle.dumps(value)
            return self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.error("Cache set failed", key=key, error=str(e))
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """Get cache value"""
        try:
            value = self.redis_client.get(key)
            if value is None:
                return None
            return pickle.loads(value)
        except Exception as e:
            logger.error("Cache get failed", key=key, error=str(e))
            return None
    
    def delete(self, key: str) -> bool:
        """Delete cache key"""
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error("Cache delete failed", key=key, error=str(e))
            return False
    
    def exists(self, key: str) -> bool:
        """Check if cache key exists"""
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error("Cache exists check failed", key=key, error=str(e))
            return False
    
    def flush_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.error("Cache flush pattern failed", pattern=pattern, error=str(e))
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            info = self.redis_client.info()
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(info),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
        except Exception as e:
            logger.error("Failed to get cache stats", error=str(e))
            return {}
    
    def _calculate_hit_rate(self, info: Dict) -> float:
        """Calculate cache hit rate"""
        hits = info.get("keyspace_hits", 0)
        misses = info.get("keyspace_misses", 0)
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0

class CacheStrategies:
    """Different caching strategies for various use cases"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
    
    def cache_user_data(self, user_id: str, data: Dict, ttl: int = 1800):
        """Cache user data with 30-minute TTL"""
        key = f"user:{user_id}"
        return self.cache.set(key, data, ttl)
    
    def get_user_data(self, user_id: str) -> Optional[Dict]:
        """Get cached user data"""
        key = f"user:{user_id}"
        return self.cache.get(key)
    
    def cache_project_data(self, project_id: str, data: Dict, ttl: int = 3600):
        """Cache project data with 1-hour TTL"""
        key = f"project:{project_id}"
        return self.cache.set(key, data, ttl)
    
    def get_project_data(self, project_id: str) -> Optional[Dict]:
        """Get cached project data"""
        key = f"project:{project_id}"
        return self.cache.get(key)
    
    def cache_ml_model_predictions(self, model_id: str, input_hash: str, prediction: Any, ttl: int = 86400):
        """Cache ML model predictions with 24-hour TTL"""
        key = f"prediction:{model_id}:{input_hash}"
        return self.cache.set(key, prediction, ttl)
    
    def get_ml_model_prediction(self, model_id: str, input_hash: str) -> Optional[Any]:
        """Get cached ML model prediction"""
        key = f"prediction:{model_id}:{input_hash}"
        return self.cache.get(key)
    
    def cache_data_analysis(self, data_source_id: str, analysis_type: str, result: Dict, ttl: int = 7200):
        """Cache data analysis results with 2-hour TTL"""
        key = f"analysis:{data_source_id}:{analysis_type}"
        return self.cache.set(key, result, ttl)
    
    def get_data_analysis(self, data_source_id: str, analysis_type: str) -> Optional[Dict]:
        """Get cached data analysis"""
        key = f"analysis:{data_source_id}:{analysis_type}"
        return self.cache.get(key)
    
    def invalidate_user_cache(self, user_id: str):
        """Invalidate all user-related cache"""
        patterns = [
            f"user:{user_id}",
            f"user:{user_id}:*",
            f"project:*:user:{user_id}",
        ]
        for pattern in patterns:
            self.cache.flush_pattern(pattern)
    
    def invalidate_project_cache(self, project_id: str):
        """Invalidate all project-related cache"""
        patterns = [
            f"project:{project_id}",
            f"project:{project_id}:*",
            f"analysis:*:project:{project_id}",
        ]
        for pattern in patterns:
            self.cache.flush_pattern(pattern)

def cache_result(prefix: str, ttl: int = 3600, key_func=None):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            cache_manager = CacheManager()
            
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug("Cache hit", key=cache_key, function=func.__name__)
                return cached_result
            
            # Execute function and cache result
            logger.debug("Cache miss", key=cache_key, function=func.__name__)
            result = await func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            cache_manager = CacheManager()
            
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug("Cache hit", key=cache_key, function=func.__name__)
                return cached_result
            
            # Execute function and cache result
            logger.debug("Cache miss", key=cache_key, function=func.__name__)
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

class SessionCache:
    """Session-based caching for user-specific data"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.session_ttl = 1800  # 30 minutes
    
    def set_session_data(self, session_id: str, key: str, value: Any):
        """Set session-specific data"""
        cache_key = f"session:{session_id}:{key}"
        return self.cache.set(cache_key, value, self.session_ttl)
    
    def get_session_data(self, session_id: str, key: str) -> Optional[Any]:
        """Get session-specific data"""
        cache_key = f"session:{session_id}:{key}"
        return self.cache.get(cache_key)
    
    def clear_session(self, session_id: str):
        """Clear all session data"""
        pattern = f"session:{session_id}:*"
        return self.cache.flush_pattern(pattern)
    
    def extend_session(self, session_id: str):
        """Extend session TTL"""
        pattern = f"session:{session_id}:*"
        keys = self.cache.redis_client.keys(pattern)
        for key in keys:
            self.cache.redis_client.expire(key, self.session_ttl)

class QueryCache:
    """Database query result caching"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.query_ttl = 600  # 10 minutes
    
    def cache_query_result(self, query_hash: str, result: Any, ttl: Optional[int] = None):
        """Cache database query result"""
        cache_key = f"query:{query_hash}"
        ttl = ttl or self.query_ttl
        return self.cache.set(cache_key, result, ttl)
    
    def get_query_result(self, query_hash: str) -> Optional[Any]:
        """Get cached query result"""
        cache_key = f"query:{query_hash}"
        return self.cache.get(cache_key)
    
    def invalidate_table_cache(self, table_name: str):
        """Invalidate all cache for a specific table"""
        pattern = f"query:*:{table_name}:*"
        return self.cache.flush_pattern(pattern)

# Initialize global cache instances
cache_manager = CacheManager()
cache_strategies = CacheStrategies(cache_manager)
session_cache = SessionCache(cache_manager)
query_cache = QueryCache(cache_manager)
