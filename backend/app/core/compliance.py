"""
Enterprise Compliance Framework
Implements SOC2, GDPR, HIPAA, and other compliance requirements
"""

import hashlib
import hmac
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, asdict
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import structlog

from app.core.config import settings

logger = structlog.get_logger()

class ComplianceFramework(Enum):
    SOC2 = "soc2"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    ISO_27001 = "iso_27001"
    CCPA = "ccpa"

class DataClassification(Enum):
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"

class PersonalDataType(Enum):
    PII = "personally_identifiable_information"
    PHI = "protected_health_information"
    FINANCIAL = "financial_information"
    BIOMETRIC = "biometric_data"
    BEHAVIORAL = "behavioral_data"

@dataclass
class DataProcessingRecord:
    """GDPR Article 30 - Records of Processing Activities"""
    id: str
    controller_name: str
    controller_contact: str
    purpose: str
    data_subjects: List[str]
    personal_data_categories: List[PersonalDataType]
    recipients: List[str]
    third_country_transfers: List[str]
    retention_period: str
    security_measures: List[str]
    created_at: datetime
    updated_at: datetime

@dataclass
class ConsentRecord:
    """GDPR Consent Management"""
    id: str
    user_id: str
    purpose: str
    consent_given: bool
    consent_date: datetime
    consent_method: str
    consent_evidence: str
    withdrawn_date: Optional[datetime] = None
    legal_basis: str = "consent"

@dataclass
class DataBreachIncident:
    """GDPR Article 33/34 - Data Breach Notification"""
    id: str
    incident_date: datetime
    discovery_date: datetime
    description: str
    affected_data_types: List[PersonalDataType]
    affected_individuals_count: int
    risk_assessment: str
    mitigation_measures: List[str]
    notification_required: bool
    notification_date: Optional[datetime] = None
    reported_to_authority: bool = False
    authority_notification_date: Optional[datetime] = None

class EncryptionManager:
    """Enterprise-grade encryption for compliance"""
    
    def __init__(self):
        self.master_key = self._derive_key(settings.ENCRYPTION_MASTER_KEY)
        self.fernet = Fernet(self.master_key)
    
    def _derive_key(self, password: str) -> bytes:
        """Derive encryption key from master password"""
        password_bytes = password.encode()
        salt = b'ai_platform_salt_2024'  # In production, use random salt per encryption
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def encrypt_pii(self, data: str) -> str:
        """Encrypt personally identifiable information"""
        encrypted_data = self.fernet.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt_pii(self, encrypted_data: str) -> str:
        """Decrypt personally identifiable information"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = self.fernet.decrypt(encrypted_bytes)
        return decrypted_data.decode()
    
    def hash_sensitive_data(self, data: str, salt: Optional[str] = None) -> str:
        """Create irreversible hash of sensitive data"""
        if salt is None:
            salt = str(uuid.uuid4())
        
        combined = f"{data}{salt}"
        hash_object = hashlib.sha256(combined.encode())
        return hash_object.hexdigest()
    
    def create_data_signature(self, data: str) -> str:
        """Create HMAC signature for data integrity"""
        signature = hmac.new(
            self.master_key,
            data.encode(),
            hashlib.sha256
        ).hexdigest()
        return signature

class GDPRCompliance:
    """GDPR Compliance Implementation"""
    
    def __init__(self):
        self.encryption_manager = EncryptionManager()
        self.processing_records: Dict[str, DataProcessingRecord] = {}
        self.consent_records: Dict[str, ConsentRecord] = {}
        self.breach_incidents: Dict[str, DataBreachIncident] = {}
    
    def register_processing_activity(
        self,
        controller_name: str,
        purpose: str,
        data_subjects: List[str],
        personal_data_categories: List[PersonalDataType],
        recipients: List[str] = None,
        third_country_transfers: List[str] = None,
        retention_period: str = "As per data retention policy",
        security_measures: List[str] = None
    ) -> str:
        """Register data processing activity (Article 30)"""
        
        record_id = str(uuid.uuid4())
        record = DataProcessingRecord(
            id=record_id,
            controller_name=controller_name,
            controller_contact=settings.DPO_CONTACT,
            purpose=purpose,
            data_subjects=data_subjects,
            personal_data_categories=personal_data_categories,
            recipients=recipients or [],
            third_country_transfers=third_country_transfers or [],
            retention_period=retention_period,
            security_measures=security_measures or [
                "Encryption at rest and in transit",
                "Access controls and authentication",
                "Regular security assessments",
                "Data minimization"
            ],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.processing_records[record_id] = record
        
        logger.info(
            "GDPR processing activity registered",
            record_id=record_id,
            controller=controller_name,
            purpose=purpose
        )
        
        return record_id
    
    def record_consent(
        self,
        user_id: str,
        purpose: str,
        consent_given: bool,
        consent_method: str,
        consent_evidence: str,
        legal_basis: str = "consent"
    ) -> str:
        """Record user consent (Article 7)"""
        
        consent_id = str(uuid.uuid4())
        consent = ConsentRecord(
            id=consent_id,
            user_id=user_id,
            purpose=purpose,
            consent_given=consent_given,
            consent_date=datetime.utcnow(),
            consent_method=consent_method,
            consent_evidence=consent_evidence,
            legal_basis=legal_basis
        )
        
        self.consent_records[consent_id] = consent
        
        logger.info(
            "GDPR consent recorded",
            consent_id=consent_id,
            user_id=user_id,
            purpose=purpose,
            consent_given=consent_given
        )
        
        return consent_id
    
    def withdraw_consent(self, user_id: str, purpose: str) -> bool:
        """Withdraw user consent (Article 7.3)"""
        
        for consent in self.consent_records.values():
            if consent.user_id == user_id and consent.purpose == purpose and consent.consent_given:
                consent.consent_given = False
                consent.withdrawn_date = datetime.utcnow()
                
                logger.info(
                    "GDPR consent withdrawn",
                    user_id=user_id,
                    purpose=purpose,
                    withdrawal_date=consent.withdrawn_date
                )
                
                return True
        
        return False
    
    def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export all user data (Article 20 - Data Portability)"""
        
        # This would collect all user data from various sources
        user_data = {
            "user_id": user_id,
            "export_date": datetime.utcnow().isoformat(),
            "data_sources": {
                "profile": {},  # User profile data
                "projects": [],  # User projects
                "models": [],    # ML models
                "workflows": [], # Workflows
                "data_sources": [], # Data sources
                "activity_logs": [] # Activity logs
            },
            "consent_records": [
                asdict(consent) for consent in self.consent_records.values()
                if consent.user_id == user_id
            ]
        }
        
        logger.info(
            "GDPR data export requested",
            user_id=user_id,
            export_date=user_data["export_date"]
        )
        
        return user_data
    
    def delete_user_data(self, user_id: str, reason: str = "User request") -> bool:
        """Delete all user data (Article 17 - Right to Erasure)"""
        
        try:
            # This would delete user data from all systems
            # Implementation would depend on specific data architecture
            
            logger.info(
                "GDPR data deletion initiated",
                user_id=user_id,
                reason=reason,
                deletion_date=datetime.utcnow()
            )
            
            return True
            
        except Exception as e:
            logger.error(
                "GDPR data deletion failed",
                user_id=user_id,
                error=str(e)
            )
            return False
    
    def report_data_breach(
        self,
        description: str,
        affected_data_types: List[PersonalDataType],
        affected_individuals_count: int,
        risk_assessment: str,
        mitigation_measures: List[str]
    ) -> str:
        """Report data breach (Article 33/34)"""
        
        incident_id = str(uuid.uuid4())
        incident = DataBreachIncident(
            id=incident_id,
            incident_date=datetime.utcnow(),
            discovery_date=datetime.utcnow(),
            description=description,
            affected_data_types=affected_data_types,
            affected_individuals_count=affected_individuals_count,
            risk_assessment=risk_assessment,
            mitigation_measures=mitigation_measures,
            notification_required=self._assess_notification_requirement(
                affected_data_types, affected_individuals_count, risk_assessment
            )
        )
        
        self.breach_incidents[incident_id] = incident
        
        logger.critical(
            "GDPR data breach reported",
            incident_id=incident_id,
            affected_count=affected_individuals_count,
            notification_required=incident.notification_required
        )
        
        # Auto-notify if required
        if incident.notification_required:
            self._notify_supervisory_authority(incident)
        
        return incident_id
    
    def _assess_notification_requirement(
        self,
        affected_data_types: List[PersonalDataType],
        affected_count: int,
        risk_assessment: str
    ) -> bool:
        """Assess if breach notification is required"""
        
        # High-risk data types always require notification
        high_risk_types = [PersonalDataType.PHI, PersonalDataType.FINANCIAL, PersonalDataType.BIOMETRIC]
        if any(data_type in high_risk_types for data_type in affected_data_types):
            return True
        
        # Large number of affected individuals
        if affected_count > 100:
            return True
        
        # High risk assessment
        if "high" in risk_assessment.lower():
            return True
        
        return False
    
    def _notify_supervisory_authority(self, incident: DataBreachIncident):
        """Notify supervisory authority within 72 hours"""
        
        # In production, this would integrate with actual notification systems
        logger.critical(
            "Supervisory authority notification required",
            incident_id=incident.id,
            deadline=incident.discovery_date + timedelta(hours=72)
        )

class SOC2Compliance:
    """SOC2 Type II Compliance Implementation"""
    
    def __init__(self):
        self.trust_service_criteria = {
            "security": "Security controls",
            "availability": "System availability",
            "processing_integrity": "Processing integrity",
            "confidentiality": "Confidentiality controls",
            "privacy": "Privacy controls"
        }
    
    def log_security_event(
        self,
        event_type: str,
        user_id: Optional[str],
        resource: str,
        action: str,
        result: str,
        ip_address: str,
        user_agent: str,
        additional_data: Dict[str, Any] = None
    ):
        """Log security events for SOC2 audit trail"""
        
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_id": str(uuid.uuid4()),
            "event_type": event_type,
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "result": result,
            "source_ip": ip_address,
            "user_agent": user_agent,
            "additional_data": additional_data or {}
        }
        
        logger.info("SOC2 security event", **event)
    
    def validate_access_controls(self, user_id: str, resource: str, action: str) -> bool:
        """Validate access controls (Security criterion)"""
        
        # Implementation would check role-based access controls
        # This is a simplified example
        
        self.log_security_event(
            event_type="access_control_check",
            user_id=user_id,
            resource=resource,
            action=action,
            result="validated",
            ip_address="system",
            user_agent="system"
        )
        
        return True
    
    def monitor_system_availability(self) -> Dict[str, Any]:
        """Monitor system availability (Availability criterion)"""
        
        availability_metrics = {
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_percentage": 99.99,
            "response_time_ms": 150,
            "error_rate_percentage": 0.01,
            "active_users": 1000,
            "system_load": 0.75
        }
        
        logger.info("SOC2 availability metrics", **availability_metrics)
        
        return availability_metrics

class HIPAACompliance:
    """HIPAA Compliance Implementation"""
    
    def __init__(self):
        self.encryption_manager = EncryptionManager()
    
    def encrypt_phi(self, phi_data: str) -> str:
        """Encrypt Protected Health Information"""
        return self.encryption_manager.encrypt_pii(phi_data)
    
    def decrypt_phi(self, encrypted_phi: str) -> str:
        """Decrypt Protected Health Information"""
        return self.encryption_manager.decrypt_pii(encrypted_phi)
    
    def log_phi_access(
        self,
        user_id: str,
        patient_id: str,
        action: str,
        phi_type: str,
        ip_address: str
    ):
        """Log PHI access for audit trail"""
        
        access_log = {
            "timestamp": datetime.utcnow().isoformat(),
            "access_id": str(uuid.uuid4()),
            "user_id": user_id,
            "patient_id": self.encryption_manager.hash_sensitive_data(patient_id),
            "action": action,
            "phi_type": phi_type,
            "source_ip": ip_address,
            "compliance_framework": "HIPAA"
        }
        
        logger.info("HIPAA PHI access", **access_log)
    
    def validate_minimum_necessary(self, requested_data: List[str], purpose: str) -> List[str]:
        """Implement minimum necessary standard"""
        
        # This would implement business logic to determine
        # the minimum necessary PHI for the given purpose
        
        allowed_data = requested_data  # Simplified implementation
        
        logger.info(
            "HIPAA minimum necessary validation",
            purpose=purpose,
            requested_fields=len(requested_data),
            allowed_fields=len(allowed_data)
        )
        
        return allowed_data

# Initialize compliance managers
encryption_manager = EncryptionManager()
gdpr_compliance = GDPRCompliance()
soc2_compliance = SOC2Compliance()
hipaa_compliance = HIPAACompliance()
