"""
Comprehensive error handling and validation for AI Data Platform
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from pydantic import ValidationError
import structlog
from typing import Any, Dict, Optional
import traceback

logger = structlog.get_logger()

class APIError(Exception):
    """Base API error class"""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or "INTERNAL_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(APIError):
    """Validation error"""
    
    def __init__(self, message: str, field: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="VALIDATION_ERROR",
            details={"field": field, **(details or {})}
        )

class AuthenticationError(APIError):
    """Authentication error"""
    
    def __init__(self, message: str = "Authentication required"):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="AUTHENTICATION_ERROR"
        )

class AuthorizationError(APIError):
    """Authorization error"""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            error_code="AUTHORIZATION_ERROR"
        )

class NotFoundError(APIError):
    """Resource not found error"""
    
    def __init__(self, resource: str, identifier: Optional[str] = None):
        message = f"{resource} not found"
        if identifier:
            message += f": {identifier}"
        
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            error_code="NOT_FOUND",
            details={"resource": resource, "identifier": identifier}
        )

class ConflictError(APIError):
    """Resource conflict error"""
    
    def __init__(self, message: str, resource: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            error_code="CONFLICT_ERROR",
            details={"resource": resource}
        )

class RateLimitError(APIError):
    """Rate limit exceeded error"""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            error_code="RATE_LIMIT_ERROR",
            details={"retry_after": retry_after}
        )

class ExternalServiceError(APIError):
    """External service error"""
    
    def __init__(self, service: str, message: str = "External service unavailable"):
        super().__init__(
            message=f"{service}: {message}",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_code="EXTERNAL_SERVICE_ERROR",
            details={"service": service}
        )

class MLError(APIError):
    """Machine learning specific error"""
    
    def __init__(self, message: str, operation: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="ML_ERROR",
            details={"operation": operation}
        )

class DataError(APIError):
    """Data processing error"""
    
    def __init__(self, message: str, data_source: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_code="DATA_ERROR",
            details={"data_source": data_source}
        )

# Error handlers
async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """Handle custom API errors"""
    
    logger.error(
        "API error occurred",
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details
            },
            "request_id": getattr(request.state, "request_id", None)
        }
    )

async def validation_error_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle Pydantic validation errors"""
    
    errors = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        errors.append({
            "field": field_path,
            "message": error["msg"],
            "type": error["type"],
            "input": error.get("input")
        })
    
    logger.warning(
        "Validation error",
        errors=errors,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Request validation failed",
                "details": {"errors": errors}
            },
            "request_id": getattr(request.state, "request_id", None)
        }
    )

async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    
    logger.warning(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": "HTTP_ERROR",
                "message": exc.detail,
                "details": {}
            },
            "request_id": getattr(request.state, "request_id", None)
        }
    )

async def database_error_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """Handle database errors"""
    
    if isinstance(exc, IntegrityError):
        error_code = "INTEGRITY_ERROR"
        message = "Data integrity constraint violation"
        status_code = status.HTTP_409_CONFLICT
    else:
        error_code = "DATABASE_ERROR"
        message = "Database operation failed"
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    
    logger.error(
        "Database error",
        error_type=type(exc).__name__,
        error_message=str(exc),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": {
                "code": error_code,
                "message": message,
                "details": {}
            },
            "request_id": getattr(request.state, "request_id", None)
        }
    )

async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions"""
    
    logger.error(
        "Unexpected error",
        error_type=type(exc).__name__,
        error_message=str(exc),
        traceback=traceback.format_exc(),
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "code": "INTERNAL_ERROR",
                "message": "An unexpected error occurred",
                "details": {}
            },
            "request_id": getattr(request.state, "request_id", None)
        }
    )

# Validation utilities
def validate_file_upload(file, max_size: int = 100 * 1024 * 1024, allowed_types: set = None):
    """Validate file upload"""
    
    if allowed_types is None:
        allowed_types = {'.csv', '.json', '.xlsx', '.parquet', '.txt'}
    
    # Check file size
    if hasattr(file, 'size') and file.size and file.size > max_size:
        raise ValidationError(
            f"File size exceeds maximum allowed size of {max_size // (1024*1024)}MB",
            field="file"
        )
    
    # Check file type
    if hasattr(file, 'filename') and file.filename:
        from pathlib import Path
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in allowed_types:
            raise ValidationError(
                f"File type {file_extension} not allowed. Allowed types: {', '.join(allowed_types)}",
                field="file"
            )

def validate_model_config(config: Dict[str, Any]):
    """Validate ML model configuration"""
    
    required_fields = ['model_type', 'target_column']
    for field in required_fields:
        if field not in config:
            raise ValidationError(f"Missing required field: {field}", field=field)
    
    # Validate model type
    valid_model_types = ['classification', 'regression', 'clustering', 'anomaly_detection']
    if config['model_type'] not in valid_model_types:
        raise ValidationError(
            f"Invalid model type. Must be one of: {', '.join(valid_model_types)}",
            field="model_type"
        )
    
    # Validate time budget
    if 'time_budget_hours' in config:
        time_budget = config['time_budget_hours']
        if not isinstance(time_budget, (int, float)) or time_budget <= 0 or time_budget > 24:
            raise ValidationError(
                "Time budget must be between 0 and 24 hours",
                field="time_budget_hours"
            )

def validate_pagination_params(page: int, size: int, max_size: int = 100):
    """Validate pagination parameters"""
    
    if page < 1:
        raise ValidationError("Page number must be >= 1", field="page")
    
    if size < 1:
        raise ValidationError("Page size must be >= 1", field="size")
    
    if size > max_size:
        raise ValidationError(f"Page size cannot exceed {max_size}", field="size")

def validate_date_range(start_date, end_date):
    """Validate date range"""
    
    if start_date and end_date and start_date > end_date:
        raise ValidationError("Start date must be before end date", field="date_range")

def validate_cloud_provider(provider: str):
    """Validate cloud provider"""
    
    valid_providers = ['aws', 'azure', 'gcp', 'multi_cloud']
    if provider not in valid_providers:
        raise ValidationError(
            f"Invalid cloud provider. Must be one of: {', '.join(valid_providers)}",
            field="cloud_provider"
        )
