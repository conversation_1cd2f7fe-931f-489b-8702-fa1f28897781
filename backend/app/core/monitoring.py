import time
import psutil
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Exception
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import redis
import structlog
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST

from app.core.config import settings
from app.core.database import get_db

logger = structlog.get_logger()

# Prometheus metrics
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
ACTIVE_CONNECTIONS = Gauge('active_connections', 'Active database connections')
CELERY_TASKS = Counter('celery_tasks_total', 'Total Celery tasks', ['task_name', 'status'])
ML_MODEL_PREDICTIONS = Counter('ml_model_predictions_total', 'Total ML model predictions', ['model_id'])
DATA_PROCESSING_DURATION = Histogram('data_processing_duration_seconds', 'Data processing duration')

class HealthChecker:
    """Comprehensive health checking system"""
    
    def __init__(self):
        self.redis_client = None
        self._initialize_redis()
    
    def _initialize_redis(self):
        """Initialize Redis connection for health checks"""
        try:
            self.redis_client = redis.from_url(settings.REDIS_URL)
        except Exception as e:
            logger.error("Failed to initialize Redis client", error=str(e))
    
    async def check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            async for db in get_db():
                start_time = time.time()
                
                # Test basic connectivity
                result = await db.execute(text("SELECT 1"))
                connectivity_time = time.time() - start_time
                
                # Test database stats
                stats_query = """
                SELECT 
                    count(*) as total_connections,
                    sum(case when state = 'active' then 1 else 0 end) as active_connections,
                    sum(case when state = 'idle' then 1 else 0 end) as idle_connections
                FROM pg_stat_activity 
                WHERE datname = current_database()
                """
                
                stats_result = await db.execute(text(stats_query))
                stats = stats_result.fetchone()
                
                return {
                    "status": "healthy",
                    "connectivity_time_ms": round(connectivity_time * 1000, 2),
                    "total_connections": stats[0] if stats else 0,
                    "active_connections": stats[1] if stats else 0,
                    "idle_connections": stats[2] if stats else 0,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def check_redis_health(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance"""
        try:
            if not self.redis_client:
                self._initialize_redis()
            
            start_time = time.time()
            
            # Test basic connectivity
            self.redis_client.ping()
            ping_time = time.time() - start_time
            
            # Get Redis info
            info = self.redis_client.info()
            
            return {
                "status": "healthy",
                "ping_time_ms": round(ping_time * 1000, 2),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown"),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Redis health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def check_system_health(self) -> Dict[str, Any]:
        """Check system resources"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Load average (Unix only)
            try:
                load_avg = psutil.getloadavg()
            except AttributeError:
                load_avg = [0, 0, 0]  # Windows doesn't have load average
            
            return {
                "status": "healthy",
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "load_average": {
                    "1min": load_avg[0],
                    "5min": load_avg[1],
                    "15min": load_avg[2]
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("System health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def check_celery_health(self) -> Dict[str, Any]:
        """Check Celery worker health"""
        try:
            from app.workers.celery_app import celery_app
            
            # Get active workers
            inspect = celery_app.control.inspect()
            active_workers = inspect.active()
            
            if not active_workers:
                return {
                    "status": "unhealthy",
                    "error": "No active Celery workers found",
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            # Get worker stats
            stats = inspect.stats()
            
            total_workers = len(active_workers)
            total_active_tasks = sum(len(tasks) for tasks in active_workers.values())
            
            return {
                "status": "healthy",
                "total_workers": total_workers,
                "active_tasks": total_active_tasks,
                "workers": list(active_workers.keys()),
                "stats": stats,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Celery health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """Run all health checks"""
        start_time = time.time()
        
        # Run all health checks concurrently
        database_health = await self.check_database_health()
        redis_health = self.check_redis_health()
        system_health = self.check_system_health()
        celery_health = await self.check_celery_health()
        
        total_time = time.time() - start_time
        
        # Determine overall health
        all_healthy = all(
            check["status"] == "healthy" 
            for check in [database_health, redis_health, system_health, celery_health]
        )
        
        return {
            "status": "healthy" if all_healthy else "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "check_duration_ms": round(total_time * 1000, 2),
            "checks": {
                "database": database_health,
                "redis": redis_health,
                "system": system_health,
                "celery": celery_health
            }
        }

class MetricsCollector:
    """Collect and expose Prometheus metrics"""
    
    @staticmethod
    def record_request(method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics"""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status_code).inc()
        REQUEST_DURATION.observe(duration)
    
    @staticmethod
    def record_celery_task(task_name: str, status: str):
        """Record Celery task metrics"""
        CELERY_TASKS.labels(task_name=task_name, status=status).inc()
    
    @staticmethod
    def record_ml_prediction(model_id: str):
        """Record ML model prediction"""
        ML_MODEL_PREDICTIONS.labels(model_id=model_id).inc()
    
    @staticmethod
    def record_data_processing(duration: float):
        """Record data processing duration"""
        DATA_PROCESSING_DURATION.observe(duration)
    
    @staticmethod
    def update_active_connections(count: int):
        """Update active database connections gauge"""
        ACTIVE_CONNECTIONS.set(count)
    
    @staticmethod
    def get_metrics():
        """Get Prometheus metrics in text format"""
        return generate_latest()

class AlertManager:
    """Manage alerts and notifications"""
    
    def __init__(self):
        self.alert_thresholds = {
            "cpu_percent": 80,
            "memory_percent": 85,
            "disk_percent": 90,
            "response_time_ms": 5000,
            "error_rate_percent": 5
        }
    
    async def check_alerts(self, health_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check for alert conditions"""
        alerts = []
        
        # System resource alerts
        system_health = health_data.get("checks", {}).get("system", {})
        
        if system_health.get("cpu_percent", 0) > self.alert_thresholds["cpu_percent"]:
            alerts.append({
                "type": "high_cpu",
                "severity": "warning",
                "message": f"High CPU usage: {system_health['cpu_percent']}%",
                "threshold": self.alert_thresholds["cpu_percent"],
                "current_value": system_health["cpu_percent"]
            })
        
        memory_percent = system_health.get("memory", {}).get("percent", 0)
        if memory_percent > self.alert_thresholds["memory_percent"]:
            alerts.append({
                "type": "high_memory",
                "severity": "warning",
                "message": f"High memory usage: {memory_percent}%",
                "threshold": self.alert_thresholds["memory_percent"],
                "current_value": memory_percent
            })
        
        disk_percent = system_health.get("disk", {}).get("percent", 0)
        if disk_percent > self.alert_thresholds["disk_percent"]:
            alerts.append({
                "type": "high_disk",
                "severity": "critical",
                "message": f"High disk usage: {disk_percent:.1f}%",
                "threshold": self.alert_thresholds["disk_percent"],
                "current_value": disk_percent
            })
        
        # Service health alerts
        for service_name, service_health in health_data.get("checks", {}).items():
            if service_health.get("status") != "healthy":
                alerts.append({
                    "type": "service_unhealthy",
                    "severity": "critical",
                    "message": f"Service {service_name} is unhealthy: {service_health.get('error', 'Unknown error')}",
                    "service": service_name
                })
        
        return alerts
    
    async def send_alert(self, alert: Dict[str, Any]):
        """Send alert notification"""
        # Log the alert
        logger.warning("Alert triggered", alert=alert)
        
        # Here you would integrate with your alerting system
        # (Slack, PagerDuty, email, etc.)
        pass

# Initialize global instances
health_checker = HealthChecker()
metrics_collector = MetricsCollector()
alert_manager = AlertManager()
