import time
import asyncio
import psutil
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from fastapi import Request, Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import structlog

from app.core.cache import cache_manager, cache_result
from app.core.monitoring import metrics_collector

logger = structlog.get_logger()

class PerformanceMonitor:
    """Performance monitoring and optimization"""
    
    def __init__(self):
        self.request_times = []
        self.slow_queries = []
        self.cache_stats = {}
        
    async def track_request_performance(self, request: Request, call_next):
        """Track request performance metrics"""
        start_time = time.time()
        
        # Get initial system metrics
        initial_memory = psutil.virtual_memory().percent
        initial_cpu = psutil.cpu_percent()
        
        # Process request
        response = await call_next(request)
        
        # Calculate metrics
        duration = time.time() - start_time
        final_memory = psutil.virtual_memory().percent
        final_cpu = psutil.cpu_percent()
        
        # Record metrics
        metrics_collector.record_request(
            method=request.method,
            endpoint=str(request.url.path),
            status_code=response.status_code,
            duration=duration
        )
        
        # Log slow requests
        if duration > 2.0:  # Requests slower than 2 seconds
            logger.warning(
                "Slow request detected",
                method=request.method,
                path=request.url.path,
                duration=duration,
                status_code=response.status_code,
                memory_delta=final_memory - initial_memory,
                cpu_delta=final_cpu - initial_cpu
            )
        
        # Add performance headers
        response.headers["X-Response-Time"] = f"{duration:.3f}s"
        response.headers["X-Memory-Usage"] = f"{final_memory:.1f}%"
        
        return response
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Cache metrics
            cache_stats = cache_manager.get_stats()
            
            # Database connection pool stats (if available)
            db_stats = self._get_db_pool_stats()
            
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_percent": (disk.used / disk.total) * 100,
                    "disk_free_gb": disk.free / (1024**3)
                },
                "cache": cache_stats,
                "database": db_stats,
                "application": {
                    "active_requests": len(self.request_times),
                    "avg_response_time": self._calculate_avg_response_time(),
                    "slow_queries_count": len(self.slow_queries)
                }
            }
        except Exception as e:
            logger.error("Failed to get performance stats", error=str(e))
            return {"error": str(e)}
    
    def _get_db_pool_stats(self) -> Dict[str, Any]:
        """Get database connection pool statistics"""
        try:
            # This would be implemented based on your connection pool
            # For SQLAlchemy async, you'd check the pool status
            return {
                "pool_size": 20,  # Configure based on your pool
                "checked_out": 0,
                "overflow": 0,
                "checked_in": 20
            }
        except Exception:
            return {}
    
    def _calculate_avg_response_time(self) -> float:
        """Calculate average response time from recent requests"""
        if not self.request_times:
            return 0.0
        
        # Keep only last 100 requests
        recent_times = self.request_times[-100:]
        return sum(recent_times) / len(recent_times)

class DatabaseOptimizer:
    """Database query optimization and monitoring"""
    
    def __init__(self):
        self.query_cache = {}
        self.slow_query_threshold = 1.0  # 1 second
    
    async def execute_with_monitoring(self, db: AsyncSession, query: str, params: Dict = None):
        """Execute database query with performance monitoring"""
        start_time = time.time()
        
        try:
            # Execute query
            if params:
                result = await db.execute(text(query), params)
            else:
                result = await db.execute(text(query))
            
            duration = time.time() - start_time
            
            # Log slow queries
            if duration > self.slow_query_threshold:
                logger.warning(
                    "Slow query detected",
                    query=query[:200],  # First 200 chars
                    duration=duration,
                    params=params
                )
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                "Query execution failed",
                query=query[:200],
                duration=duration,
                error=str(e)
            )
            raise
    
    @cache_result("query_plan", ttl=3600)
    async def get_query_plan(self, db: AsyncSession, query: str) -> Dict[str, Any]:
        """Get and cache query execution plan"""
        try:
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            result = await db.execute(text(explain_query))
            plan = result.fetchone()[0]
            return plan[0] if plan else {}
        except Exception as e:
            logger.error("Failed to get query plan", error=str(e))
            return {}
    
    async def analyze_table_stats(self, db: AsyncSession, table_name: str) -> Dict[str, Any]:
        """Analyze table statistics for optimization"""
        try:
            stats_query = """
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation,
                most_common_vals,
                most_common_freqs
            FROM pg_stats 
            WHERE tablename = :table_name
            """
            
            result = await db.execute(text(stats_query), {"table_name": table_name})
            stats = result.fetchall()
            
            return {
                "table_name": table_name,
                "column_stats": [dict(row) for row in stats],
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Failed to analyze table stats", table=table_name, error=str(e))
            return {}

class CacheOptimizer:
    """Cache optimization and management"""
    
    def __init__(self):
        self.hit_rate_threshold = 80.0  # 80% hit rate target
        
    def analyze_cache_performance(self) -> Dict[str, Any]:
        """Analyze cache performance and suggest optimizations"""
        stats = cache_manager.get_stats()
        
        recommendations = []
        
        # Check hit rate
        hit_rate = stats.get("hit_rate", 0)
        if hit_rate < self.hit_rate_threshold:
            recommendations.append({
                "type": "low_hit_rate",
                "message": f"Cache hit rate is {hit_rate:.1f}%, consider increasing TTL or cache size",
                "severity": "warning"
            })
        
        # Check memory usage
        memory_usage = stats.get("used_memory", 0)
        if memory_usage > 400 * 1024 * 1024:  # 400MB
            recommendations.append({
                "type": "high_memory_usage",
                "message": "Cache memory usage is high, consider implementing LRU eviction",
                "severity": "warning"
            })
        
        return {
            "stats": stats,
            "recommendations": recommendations,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def optimize_cache_keys(self) -> List[str]:
        """Identify and clean up unused cache keys"""
        try:
            # Get all keys
            all_keys = cache_manager.redis_client.keys("*")
            
            # Identify patterns for cleanup
            expired_patterns = []
            
            # Check for old session keys (older than 24 hours)
            session_keys = [k for k in all_keys if k.startswith(b"session:")]
            for key in session_keys:
                ttl = cache_manager.redis_client.ttl(key)
                if ttl == -1:  # No expiration set
                    expired_patterns.append(key.decode())
            
            # Clean up expired keys
            if expired_patterns:
                cache_manager.redis_client.delete(*expired_patterns)
            
            return expired_patterns
            
        except Exception as e:
            logger.error("Failed to optimize cache keys", error=str(e))
            return []

class ResourceOptimizer:
    """System resource optimization"""
    
    def __init__(self):
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.disk_threshold = 90.0
    
    def check_resource_usage(self) -> Dict[str, Any]:
        """Check current resource usage and suggest optimizations"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            alerts = []
            recommendations = []
            
            # CPU checks
            if cpu_percent > self.cpu_threshold:
                alerts.append({
                    "type": "high_cpu",
                    "value": cpu_percent,
                    "threshold": self.cpu_threshold,
                    "severity": "warning"
                })
                recommendations.append("Consider scaling horizontally or optimizing CPU-intensive operations")
            
            # Memory checks
            if memory.percent > self.memory_threshold:
                alerts.append({
                    "type": "high_memory",
                    "value": memory.percent,
                    "threshold": self.memory_threshold,
                    "severity": "warning"
                })
                recommendations.append("Consider increasing memory or implementing memory optimization")
            
            # Disk checks
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > self.disk_threshold:
                alerts.append({
                    "type": "high_disk",
                    "value": disk_percent,
                    "threshold": self.disk_threshold,
                    "severity": "critical"
                })
                recommendations.append("Clean up old files or increase disk space")
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk_percent,
                "alerts": alerts,
                "recommendations": recommendations,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("Failed to check resource usage", error=str(e))
            return {"error": str(e)}

# Initialize global instances
performance_monitor = PerformanceMonitor()
db_optimizer = DatabaseOptimizer()
cache_optimizer = CacheOptimizer()
resource_optimizer = ResourceOptimizer()
