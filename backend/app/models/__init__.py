# Import all models to ensure they are registered with SQLAlchemy
from app.models.user import User, UserRole
from app.models.project import Project, ProjectStatus, ProjectType
from app.models.data_source import DataSource, DataSourceType, DataSourceStatus
from app.models.pipeline import Pipeline, PipelineStatus, PipelineType
from app.models.ml_model import MLModel, ModelStatus, ModelType, MLFramework
from app.models.workflow import Workflow, WorkflowStatus, WorkflowType
from app.models.compliance import (
    ConsentRecord, DataProcessingRecord, DataSubjectRequest,
    AuditLog, DataBreachIncident, ComplianceAssessment,
    RetentionPolicy, PrivacyImpactAssessment
)

__all__ = [
    # User models
    "User",
    "UserRole",

    # Project models
    "Project",
    "ProjectStatus",
    "ProjectType",

    # Data models
    "DataSource",
    "DataSourceType",
    "DataSourceStatus",

    # Pipeline models
    "Pipeline",
    "PipelineStatus",
    "PipelineType",

    # ML models
    "MLModel",
    "ModelStatus",
    "ModelType",
    "MLFramework",

    # Workflow models
    "Workflow",
    "WorkflowStatus",
    "WorkflowType",

    # Compliance models
    "ConsentRecord",
    "DataProcessingRecord",
    "DataSubjectRequest",
    "AuditLog",
    "DataBreachIncident",
    "ComplianceAssessment",
    "RetentionPolicy",
    "PrivacyImpactAssessment",
]
