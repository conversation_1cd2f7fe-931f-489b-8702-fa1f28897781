"""
Compliance and audit models for GDPR, SOC2, and other regulations
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from app.core.database import Base

class ConsentRecord(Base):
    """GDPR consent records (Article 7)"""
    
    __tablename__ = "consent_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    purpose = Column(String(255), nullable=False)  # Purpose of data processing
    consent_given = Column(Boolean, nullable=False)
    consent_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    consent_method = Column(String(100), nullable=False)  # web_form, api, email, etc.
    consent_evidence = Column(Text)  # Evidence of consent
    legal_basis = Column(String(50), nullable=False, default="consent")  # GDPR legal basis
    ip_address = Column(String(45))  # IPv4/IPv6 address
    user_agent = Column(Text)  # Browser/client information
    withdrawn_at = Column(DateTime)  # When consent was withdrawn
    withdrawal_method = Column(String(100))  # How consent was withdrawn
    
    # Relationships
    user = relationship("User", back_populates="consent_records")
    
    # Indexes
    __table_args__ = (
        Index('idx_consent_user_purpose', 'user_id', 'purpose'),
        Index('idx_consent_date', 'consent_date'),
    )

class DataProcessingRecord(Base):
    """Record of data processing activities (Article 30)"""
    
    __tablename__ = "data_processing_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    processing_purpose = Column(String(255), nullable=False)
    data_categories = Column(JSON)  # Categories of personal data
    legal_basis = Column(String(50), nullable=False)
    retention_period = Column(String(100))  # How long data is kept
    recipients = Column(JSON)  # Who receives the data
    third_country_transfers = Column(JSON)  # International transfers
    security_measures = Column(JSON)  # Technical and organizational measures
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index('idx_processing_user', 'user_id'),
        Index('idx_processing_purpose', 'processing_purpose'),
        Index('idx_processing_created', 'created_at'),
    )

class DataSubjectRequest(Base):
    """GDPR data subject requests (Chapter III)"""
    
    __tablename__ = "data_subject_requests"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    request_type = Column(String(50), nullable=False)  # access, rectification, erasure, etc.
    status = Column(String(50), nullable=False, default="pending")
    request_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    completion_date = Column(DateTime)
    request_details = Column(JSON)  # Specific details of the request
    response_data = Column(JSON)  # Response provided to the user
    verification_method = Column(String(100))  # How identity was verified
    notes = Column(Text)  # Internal notes
    
    # Relationships
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index('idx_dsr_user', 'user_id'),
        Index('idx_dsr_type_status', 'request_type', 'status'),
        Index('idx_dsr_date', 'request_date'),
    )

class AuditLog(Base):
    """Comprehensive audit log for compliance"""
    
    __tablename__ = "audit_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    action = Column(String(100), nullable=False)  # Action performed
    resource_type = Column(String(50))  # Type of resource affected
    resource_id = Column(String(255))  # ID of the resource
    details = Column(JSON)  # Additional details
    ip_address = Column(String(45))
    user_agent = Column(Text)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    session_id = Column(String(255))  # Session identifier
    request_id = Column(String(255))  # Request identifier
    
    # Compliance-specific fields
    compliance_category = Column(String(50))  # GDPR, SOC2, etc.
    risk_level = Column(String(20), default="low")  # low, medium, high, critical
    
    # Relationships
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index('idx_audit_user', 'user_id'),
        Index('idx_audit_action', 'action'),
        Index('idx_audit_timestamp', 'timestamp'),
        Index('idx_audit_resource', 'resource_type', 'resource_id'),
        Index('idx_audit_compliance', 'compliance_category'),
    )

class DataBreachIncident(Base):
    """Data breach incident tracking (Article 33-34)"""
    
    __tablename__ = "data_breach_incidents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    incident_id = Column(String(50), unique=True, nullable=False)  # Human-readable ID
    severity = Column(String(20), nullable=False)  # low, medium, high, critical
    status = Column(String(50), nullable=False, default="investigating")
    discovered_at = Column(DateTime, nullable=False)
    reported_at = Column(DateTime)  # When reported to authorities
    contained_at = Column(DateTime)  # When breach was contained
    resolved_at = Column(DateTime)  # When fully resolved
    
    # Breach details
    description = Column(Text, nullable=False)
    affected_data_types = Column(JSON)  # Types of data affected
    affected_users_count = Column(String(50))  # Number or range of affected users
    potential_impact = Column(Text)  # Assessment of potential impact
    root_cause = Column(Text)  # Root cause analysis
    
    # Response actions
    containment_actions = Column(JSON)  # Actions taken to contain breach
    notification_actions = Column(JSON)  # Notifications sent
    remediation_actions = Column(JSON)  # Actions to prevent recurrence
    
    # Compliance
    authority_notified = Column(Boolean, default=False)
    users_notified = Column(Boolean, default=False)
    notification_deadline = Column(DateTime)  # 72-hour deadline
    
    # Indexes
    __table_args__ = (
        Index('idx_breach_severity', 'severity'),
        Index('idx_breach_status', 'status'),
        Index('idx_breach_discovered', 'discovered_at'),
    )

class ComplianceAssessment(Base):
    """Regular compliance assessments and audits"""
    
    __tablename__ = "compliance_assessments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    assessment_type = Column(String(50), nullable=False)  # GDPR, SOC2, ISO27001, etc.
    status = Column(String(50), nullable=False, default="in_progress")
    started_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    completed_at = Column(DateTime)
    assessor = Column(String(255))  # Who conducted the assessment
    
    # Assessment results
    overall_score = Column(String(20))  # Pass/Fail or percentage
    findings = Column(JSON)  # Detailed findings
    recommendations = Column(JSON)  # Recommendations for improvement
    action_items = Column(JSON)  # Required actions
    
    # Documentation
    evidence_collected = Column(JSON)  # Evidence and documentation
    report_url = Column(String(500))  # Link to full report
    
    # Indexes
    __table_args__ = (
        Index('idx_assessment_type', 'assessment_type'),
        Index('idx_assessment_status', 'status'),
        Index('idx_assessment_date', 'started_at'),
    )

class RetentionPolicy(Base):
    """Data retention policies and schedules"""
    
    __tablename__ = "retention_policies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    data_category = Column(String(100), nullable=False)  # Category of data
    retention_period = Column(String(100), nullable=False)  # How long to keep
    legal_basis = Column(String(255))  # Legal requirement for retention
    deletion_method = Column(String(100))  # How data is deleted
    exceptions = Column(JSON)  # Exceptions to the policy
    
    # Policy details
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)
    effective_date = Column(DateTime, nullable=False)
    review_date = Column(DateTime)  # When policy should be reviewed
    
    # Approval
    approved_by = Column(String(255))
    approval_date = Column(DateTime)
    
    # Indexes
    __table_args__ = (
        Index('idx_retention_category', 'data_category'),
        Index('idx_retention_effective', 'effective_date'),
    )

class PrivacyImpactAssessment(Base):
    """Privacy Impact Assessments (Article 35)"""
    
    __tablename__ = "privacy_impact_assessments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    status = Column(String(50), nullable=False, default="draft")
    
    # Assessment details
    data_types = Column(JSON)  # Types of personal data processed
    processing_purposes = Column(JSON)  # Purposes of processing
    legal_basis = Column(JSON)  # Legal basis for processing
    data_subjects = Column(JSON)  # Categories of data subjects
    recipients = Column(JSON)  # Who will receive the data
    
    # Risk assessment
    privacy_risks = Column(JSON)  # Identified privacy risks
    risk_mitigation = Column(JSON)  # Measures to mitigate risks
    residual_risks = Column(JSON)  # Remaining risks after mitigation
    
    # Approval and review
    conducted_by = Column(String(255))
    reviewed_by = Column(String(255))
    approved_by = Column(String(255))
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)
    review_date = Column(DateTime)
    
    # Indexes
    __table_args__ = (
        Index('idx_pia_status', 'status'),
        Index('idx_pia_created', 'created_at'),
    )
