from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, Dict, Any
import enum

from app.core.database import Base


class DataSourceType(str, enum.Enum):
    DATABASE = "database"
    FILE = "file"
    API = "api"
    STREAM = "stream"
    CLOUD_STORAGE = "cloud_storage"


class DataSourceStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"


class DataSource(Base):
    __tablename__ = "data_sources"
    __table_args__ = {"schema": "data_management"}
    
    # Basic Information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    type: Mapped[DataSourceType] = mapped_column(
        SQLEnum(DataSourceType, name="data_source_type"),
        nullable=False
    )
    status: Mapped[DataSourceStatus] = mapped_column(
        SQLEnum(DataSourceStatus, name="data_source_status"),
        default=DataSourceStatus.INACTIVE
    )
    
    # Connection Configuration
    connection_config: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    credentials: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)  # Encrypted
    
    # Schema and Metadata
    schema_info: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    # Settings
    is_public: Mapped[bool] = mapped_column(Boolean, default=False)
    auto_sync: Mapped[bool] = mapped_column(Boolean, default=False)
    sync_frequency: Mapped[Optional[str]] = mapped_column(String(50))  # cron expression
    
    # Health and Monitoring
    last_sync: Mapped[Optional[str]] = mapped_column(String(50))  # ISO timestamp
    last_error: Mapped[Optional[str]] = mapped_column(Text)
    health_check_url: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Relationships
    created_by_id: Mapped[str] = mapped_column(String, ForeignKey("auth.users.id"))
    project_id: Mapped[Optional[str]] = mapped_column(String, ForeignKey("data_management.projects.id"))
    
    # created_by = relationship("User", back_populates="data_sources")
    # project = relationship("Project", back_populates="data_sources")
    # pipelines = relationship("Pipeline", back_populates="data_source")
    
    def __repr__(self):
        return f"<DataSource(id={self.id}, name={self.name}, type={self.type})>"
    
    @property
    def is_healthy(self) -> bool:
        return self.status == DataSourceStatus.ACTIVE and not self.last_error
    
    def get_connection_string(self) -> Optional[str]:
        """Get connection string based on type and config"""
        if self.type == DataSourceType.DATABASE:
            config = self.connection_config
            if all(key in config for key in ['host', 'port', 'database', 'username']):
                return f"postgresql://{config['username']}:***@{config['host']}:{config['port']}/{config['database']}"
        elif self.type == DataSourceType.API:
            return self.connection_config.get('base_url')
        elif self.type == DataSourceType.CLOUD_STORAGE:
            return self.connection_config.get('bucket_url')
        return None
