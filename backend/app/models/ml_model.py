from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float, In<PERSON><PERSON>, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, Dict, Any, List
import enum

from app.core.database import Base


class ModelStatus(str, enum.Enum):
    TRAINING = "training"
    TRAINED = "trained"
    DEPLOYED = "deployed"
    ARCHIVED = "archived"
    FAILED = "failed"


class ModelType(str, enum.Enum):
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    CLUSTERING = "clustering"
    TIME_SERIES = "time_series"
    NLP = "nlp"
    COMPUTER_VISION = "computer_vision"
    DEEP_LEARNING = "deep_learning"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    GENERATIVE_AI = "generative_ai"


class MLFramework(str, enum.Enum):
    SCIKIT_LEARN = "scikit_learn"
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    XGBOOST = "xgboost"
    LIGHTGBM = "lightgbm"
    CATBOOST = "catboost"
    AUTOGLUON = "autogluon"
    HUGGINGFACE = "huggingface"
    CUSTOM = "custom"


class MLModel(Base):
    __tablename__ = "ml_models"
    __table_args__ = {"schema": "ml_models"}
    
    # Basic Information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    version: Mapped[str] = mapped_column(String(50), default="1.0.0")
    
    # Model Classification
    model_type: Mapped[ModelType] = mapped_column(
        SQLEnum(ModelType, name="model_type"),
        nullable=False
    )
    framework: Mapped[MLFramework] = mapped_column(
        SQLEnum(MLFramework, name="ml_framework"),
        nullable=False
    )
    algorithm: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Status and Lifecycle
    status: Mapped[ModelStatus] = mapped_column(
        SQLEnum(ModelStatus, name="model_status"),
        default=ModelStatus.TRAINING
    )
    
    # Training Configuration
    training_config: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    hyperparameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    feature_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    # Model Artifacts
    model_path: Mapped[Optional[str]] = mapped_column(String(500))  # Path to saved model
    model_size_mb: Mapped[Optional[float]] = mapped_column(Float)
    model_format: Mapped[Optional[str]] = mapped_column(String(50))  # pickle, joblib, onnx, etc.
    
    # Performance Metrics
    metrics: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    validation_score: Mapped[Optional[float]] = mapped_column(Float)
    test_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Training Information
    training_dataset_id: Mapped[Optional[str]] = mapped_column(String)
    training_samples: Mapped[Optional[int]] = mapped_column(Integer)
    training_duration_seconds: Mapped[Optional[int]] = mapped_column(Integer)
    training_start: Mapped[Optional[str]] = mapped_column(String(50))  # ISO timestamp
    training_end: Mapped[Optional[str]] = mapped_column(String(50))  # ISO timestamp
    
    # Deployment Information
    deployment_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    endpoint_url: Mapped[Optional[str]] = mapped_column(String(500))
    deployment_status: Mapped[Optional[str]] = mapped_column(String(50))
    
    # Monitoring
    prediction_count: Mapped[int] = mapped_column(Integer, default=0)
    last_prediction: Mapped[Optional[str]] = mapped_column(String(50))  # ISO timestamp
    drift_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Metadata
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON)
    metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    # Relationships
    created_by_id: Mapped[str] = mapped_column(String, ForeignKey("auth.users.id"))
    project_id: Mapped[Optional[str]] = mapped_column(String, ForeignKey("data_management.projects.id"))
    pipeline_id: Mapped[Optional[str]] = mapped_column(String, ForeignKey("workflows.pipelines.id"))
    
    # created_by = relationship("User", back_populates="models")
    # project = relationship("Project", back_populates="models")
    # pipeline = relationship("Pipeline", back_populates="models")
    
    def __repr__(self):
        return f"<MLModel(id={self.id}, name={self.name}, type={self.model_type}, status={self.status})>"
    
    @property
    def is_deployed(self) -> bool:
        return self.status == ModelStatus.DEPLOYED and self.endpoint_url is not None
    
    @property
    def is_trainable(self) -> bool:
        return self.status in [ModelStatus.FAILED, ModelStatus.ARCHIVED] or self.status == ModelStatus.TRAINING
    
    @property
    def performance_summary(self) -> Dict[str, Any]:
        return {
            "validation_score": self.validation_score,
            "test_score": self.test_score,
            "prediction_count": self.prediction_count,
            "drift_score": self.drift_score,
            "training_duration": self.training_duration_seconds
        }
