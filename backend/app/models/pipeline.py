from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, Dict, Any, List
import enum

from app.core.database import Base


class PipelineStatus(str, enum.Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"


class PipelineType(str, enum.Enum):
    ETL = "etl"
    ELT = "elt"
    STREAMING = "streaming"
    BATCH = "batch"
    ML_TRAINING = "ml_training"
    ML_INFERENCE = "ml_inference"


class Pipeline(Base):
    __tablename__ = "pipelines"
    __table_args__ = {"schema": "workflows"}
    
    # Basic Information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    type: Mapped[PipelineType] = mapped_column(
        SQLEnum(PipelineType, name="pipeline_type"),
        default=Pi<PERSON>ineType.ETL
    )
    status: Mapped[PipelineStatus] = mapped_column(
        SQLEnum(PipelineStatus, name="pipeline_status"),
        default=PipelineStatus.DRAFT
    )
    
    # Pipeline Definition
    definition: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)  # Visual pipeline definition
    config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)  # Runtime configuration
    
    # Scheduling
    is_scheduled: Mapped[bool] = mapped_column(Boolean, default=False)
    schedule_expression: Mapped[Optional[str]] = mapped_column(String(100))  # cron expression
    timezone: Mapped[str] = mapped_column(String(50), default="UTC")
    
    # Execution Settings
    max_retries: Mapped[int] = mapped_column(Integer, default=3)
    timeout_minutes: Mapped[Optional[int]] = mapped_column(Integer)
    parallel_execution: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Monitoring and Logging
    last_run_id: Mapped[Optional[str]] = mapped_column(String)
    last_run_status: Mapped[Optional[str]] = mapped_column(String(50))
    last_run_start: Mapped[Optional[str]] = mapped_column(String(50))  # ISO timestamp
    last_run_end: Mapped[Optional[str]] = mapped_column(String(50))  # ISO timestamp
    last_error: Mapped[Optional[str]] = mapped_column(Text)
    
    # Statistics
    total_runs: Mapped[int] = mapped_column(Integer, default=0)
    successful_runs: Mapped[int] = mapped_column(Integer, default=0)
    failed_runs: Mapped[int] = mapped_column(Integer, default=0)
    avg_duration_seconds: Mapped[Optional[float]] = mapped_column()
    
    # Relationships
    created_by_id: Mapped[str] = mapped_column(String, ForeignKey("auth.users.id"))
    project_id: Mapped[Optional[str]] = mapped_column(String, ForeignKey("data_management.projects.id"))
    data_source_id: Mapped[Optional[str]] = mapped_column(String, ForeignKey("data_management.data_sources.id"))
    
    # created_by = relationship("User", back_populates="pipelines")
    # project = relationship("Project", back_populates="pipelines")
    # data_source = relationship("DataSource", back_populates="pipelines")
    # runs = relationship("PipelineRun", back_populates="pipeline")
    
    def __repr__(self):
        return f"<Pipeline(id={self.id}, name={self.name}, status={self.status})>"
    
    @property
    def success_rate(self) -> float:
        if self.total_runs == 0:
            return 0.0
        return (self.successful_runs / self.total_runs) * 100
    
    @property
    def is_running(self) -> bool:
        return self.last_run_status == "running"
    
    @property
    def can_execute(self) -> bool:
        return self.status in [PipelineStatus.ACTIVE, PipelineStatus.DRAFT] and not self.is_running
