from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, Dict, Any, List
import enum

from app.core.database import Base


class ProjectStatus(str, enum.Enum):
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


class ProjectType(str, enum.Enum):
    DATA_SCIENCE = "data_science"
    DATA_ENGINEERING = "data_engineering"
    ANALYTICS = "analytics"
    ML_RESEARCH = "ml_research"
    PRODUCTION = "production"


class Project(Base):
    __tablename__ = "projects"
    __table_args__ = {"schema": "data_management"}
    
    # Basic Information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    type: Mapped[ProjectType] = mapped_column(
        SQLEnum(ProjectType, name="project_type"),
        default=ProjectType.DATA_SCIENCE
    )
    status: Mapped[ProjectStatus] = mapped_column(
        SQLEnum(ProjectStatus, name="project_status"),
        default=ProjectStatus.ACTIVE
    )
    
    # Project Settings
    is_public: Mapped[bool] = mapped_column(Boolean, default=False)
    is_template: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Metadata
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON)
    metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    # Configuration
    settings: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    # Statistics
    data_sources_count: Mapped[int] = mapped_column(Integer, default=0)
    pipelines_count: Mapped[int] = mapped_column(Integer, default=0)
    models_count: Mapped[int] = mapped_column(Integer, default=0)
    workflows_count: Mapped[int] = mapped_column(Integer, default=0)
    
    # Relationships
    owner_id: Mapped[str] = mapped_column(String, ForeignKey("auth.users.id"))
    
    # owner = relationship("User", back_populates="projects")
    # data_sources = relationship("DataSource", back_populates="project")
    # pipelines = relationship("Pipeline", back_populates="project")
    # models = relationship("MLModel", back_populates="project")
    # workflows = relationship("Workflow", back_populates="project")
    # members = relationship("ProjectMember", back_populates="project")
    
    def __repr__(self):
        return f"<Project(id={self.id}, name={self.name}, type={self.type}, status={self.status})>"
    
    @property
    def is_active(self) -> bool:
        return self.status == ProjectStatus.ACTIVE
    
    @property
    def total_assets(self) -> int:
        return (
            self.data_sources_count + 
            self.pipelines_count + 
            self.models_count + 
            self.workflows_count
        )
