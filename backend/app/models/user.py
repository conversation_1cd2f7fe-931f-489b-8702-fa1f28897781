from sqlalchemy import String, Bo<PERSON>an, DateTime, Text, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from typing import Optional, List
import enum

from app.core.database import Base


class UserRole(str, enum.Enum):
    ADMIN = "admin"
    DATA_SCIENTIST = "data_scientist"
    DATA_ENGINEER = "data_engineer"
    ANALYST = "analyst"
    VIEWER = "viewer"


class User(Base):
    __tablename__ = "users"
    __table_args__ = {"schema": "auth"}
    
    # Basic Information
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    full_name: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Authentication
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    is_active: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=True)
    is_verified: Mapped[bool] = mapped_column(<PERSON>olean, default=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Role and Permissions
    role: Mapped[UserRole] = mapped_column(
        SQLEnum(UserRole, name="user_role"),
        default=UserRole.VIEWER
    )
    
    # Profile Information
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500))
    bio: Mapped[Optional[str]] = mapped_column(Text)
    company: Mapped[Optional[str]] = mapped_column(String(255))
    location: Mapped[Optional[str]] = mapped_column(String(255))
    website: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Timestamps
    last_login: Mapped[Optional[DateTime]] = mapped_column(DateTime(timezone=True))
    email_verified_at: Mapped[Optional[DateTime]] = mapped_column(DateTime(timezone=True))
    
    # OAuth Information
    oauth_provider: Mapped[Optional[str]] = mapped_column(String(50))
    oauth_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Preferences
    timezone: Mapped[str] = mapped_column(String(50), default="UTC")
    language: Mapped[str] = mapped_column(String(10), default="en")
    theme: Mapped[str] = mapped_column(String(20), default="light")
    
    # Relationships (will be added as we create other models)
    # projects = relationship("Project", back_populates="owner")
    # data_sources = relationship("DataSource", back_populates="created_by")
    # pipelines = relationship("Pipeline", back_populates="created_by")
    # models = relationship("MLModel", back_populates="created_by")
    
    def __repr__(self):
        return f"<User(id={self.id}, username={self.username}, email={self.email})>"
    
    @property
    def is_admin(self) -> bool:
        return self.role == UserRole.ADMIN or self.is_superuser
    
    @property
    def can_create_projects(self) -> bool:
        return self.role in [UserRole.ADMIN, UserRole.DATA_SCIENTIST, UserRole.DATA_ENGINEER]
    
    @property
    def can_manage_data(self) -> bool:
        return self.role in [UserRole.ADMIN, UserRole.DATA_SCIENTIST, UserRole.DATA_ENGINEER, UserRole.ANALYST]
    
    @property
    def can_train_models(self) -> bool:
        return self.role in [UserRole.ADMIN, UserRole.DATA_SCIENTIST]
