from sqlalchemy import String, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, Dict, Any, List
import enum

from app.core.database import Base


class WorkflowStatus(str, enum.Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"


class WorkflowType(str, enum.Enum):
    DATA_PIPELINE = "data_pipeline"
    ML_PIPELINE = "ml_pipeline"
    ANALYSIS_WORKFLOW = "analysis_workflow"
    DEPLOYMENT_WORKFLOW = "deployment_workflow"
    MONITORING_WORKFLOW = "monitoring_workflow"


class Workflow(Base):
    __tablename__ = "workflows"
    __table_args__ = {"schema": "workflows"}
    
    # Basic Information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    type: Mapped[WorkflowType] = mapped_column(
        SQLEnum(WorkflowType, name="workflow_type"),
        nullable=False
    )
    status: Mapped[WorkflowStatus] = mapped_column(
        SQLEnum(WorkflowStatus, name="workflow_status"),
        default=WorkflowStatus.DRAFT
    )
    
    # Workflow Definition (Visual No-Code Definition)
    definition: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    nodes: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, default=list)
    edges: Mapped[List[Dict[str, Any]]] = mapped_column(JSON, default=list)
    
    # Configuration
    config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    variables: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON)
    
    # Scheduling
    is_scheduled: Mapped[bool] = mapped_column(Boolean, default=False)
    schedule_expression: Mapped[Optional[str]] = mapped_column(String(100))
    timezone: Mapped[str] = mapped_column(String(50), default="UTC")
    
    # Execution Settings
    max_retries: Mapped[int] = mapped_column(Integer, default=3)
    timeout_minutes: Mapped[Optional[int]] = mapped_column(Integer)
    parallel_execution: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # Monitoring
    last_execution_id: Mapped[Optional[str]] = mapped_column(String)
    last_execution_status: Mapped[Optional[str]] = mapped_column(String(50))
    last_execution_start: Mapped[Optional[str]] = mapped_column(String(50))
    last_execution_end: Mapped[Optional[str]] = mapped_column(String(50))
    last_error: Mapped[Optional[str]] = mapped_column(Text)
    
    # Statistics
    total_executions: Mapped[int] = mapped_column(Integer, default=0)
    successful_executions: Mapped[int] = mapped_column(Integer, default=0)
    failed_executions: Mapped[int] = mapped_column(Integer, default=0)
    avg_duration_seconds: Mapped[Optional[float]] = mapped_column()
    
    # Collaboration
    is_public: Mapped[bool] = mapped_column(Boolean, default=False)
    is_template: Mapped[bool] = mapped_column(Boolean, default=False)
    tags: Mapped[Optional[List[str]]] = mapped_column(JSON)
    
    # Relationships
    created_by_id: Mapped[str] = mapped_column(String, ForeignKey("auth.users.id"))
    project_id: Mapped[Optional[str]] = mapped_column(String, ForeignKey("data_management.projects.id"))
    
    # created_by = relationship("User", back_populates="workflows")
    # project = relationship("Project", back_populates="workflows")
    # executions = relationship("WorkflowExecution", back_populates="workflow")
    
    def __repr__(self):
        return f"<Workflow(id={self.id}, name={self.name}, type={self.type}, status={self.status})>"
    
    @property
    def success_rate(self) -> float:
        if self.total_executions == 0:
            return 0.0
        return (self.successful_executions / self.total_executions) * 100
    
    @property
    def is_executing(self) -> bool:
        return self.last_execution_status == "running"
    
    @property
    def can_execute(self) -> bool:
        return self.status in [WorkflowStatus.ACTIVE, WorkflowStatus.DRAFT] and not self.is_executing
    
    @property
    def node_count(self) -> int:
        return len(self.nodes) if self.nodes else 0
    
    @property
    def edge_count(self) -> int:
        return len(self.edges) if self.edges else 0
