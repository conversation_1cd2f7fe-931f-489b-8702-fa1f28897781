import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import json
import logging
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.data_source import DataSource, DataSourceType

logger = logging.getLogger(__name__)


class DataProcessor:
    """Service for processing and analyzing data files"""
    
    def __init__(self):
        self.supported_formats = {
            '.csv': self._process_csv,
            '.json': self._process_json,
            '.xlsx': self._process_excel,
            '.parquet': self._process_parquet,
            '.txt': self._process_text
        }
    
    async def process_file(self, file_path: str, file_type: str = None) -> Dict[str, Any]:
        """Process a data file and return analysis results"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Determine file type
            if not file_type:
                file_type = file_path.suffix.lower()
            
            if file_type not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {file_type}")
            
            # Process the file
            processor = self.supported_formats[file_type]
            result = await processor(file_path)
            
            # Add file metadata
            result.update({
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'file_type': file_type,
                'processed_at': pd.Timestamp.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            raise
    
    async def _process_csv(self, file_path: Path) -> Dict[str, Any]:
        """Process CSV file"""
        try:
            # Read with different encodings if needed
            encodings = ['utf-8', 'latin-1', 'cp1252']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding, nrows=1000)  # Limit for analysis
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                raise ValueError("Could not read CSV file with any supported encoding")
            
            return self._analyze_dataframe(df)
            
        except Exception as e:
            logger.error(f"Error processing CSV file: {str(e)}")
            raise
    
    async def _process_json(self, file_path: Path) -> Dict[str, Any]:
        """Process JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convert to DataFrame if possible
            if isinstance(data, list) and len(data) > 0:
                if isinstance(data[0], dict):
                    df = pd.DataFrame(data[:1000])  # Limit for analysis
                    return self._analyze_dataframe(df)
            
            # Return basic analysis for non-tabular JSON
            return {
                'type': 'json',
                'structure': type(data).__name__,
                'size': len(data) if hasattr(data, '__len__') else 1,
                'sample': str(data)[:500] if len(str(data)) > 500 else str(data)
            }
            
        except Exception as e:
            logger.error(f"Error processing JSON file: {str(e)}")
            raise
    
    async def _process_excel(self, file_path: Path) -> Dict[str, Any]:
        """Process Excel file"""
        try:
            # Read first sheet
            df = pd.read_excel(file_path, nrows=1000)  # Limit for analysis
            return self._analyze_dataframe(df)
            
        except Exception as e:
            logger.error(f"Error processing Excel file: {str(e)}")
            raise
    
    async def _process_parquet(self, file_path: Path) -> Dict[str, Any]:
        """Process Parquet file"""
        try:
            df = pd.read_parquet(file_path)
            # Limit rows for analysis
            if len(df) > 1000:
                df = df.head(1000)
            return self._analyze_dataframe(df)
            
        except Exception as e:
            logger.error(f"Error processing Parquet file: {str(e)}")
            raise
    
    async def _process_text(self, file_path: Path) -> Dict[str, Any]:
        """Process text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            return {
                'type': 'text',
                'lines': len(lines),
                'characters': len(content),
                'words': len(content.split()),
                'sample': content[:500] if len(content) > 500 else content
            }
            
        except Exception as e:
            logger.error(f"Error processing text file: {str(e)}")
            raise
    
    def _analyze_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze a pandas DataFrame and return insights"""
        try:
            analysis = {
                'type': 'tabular',
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': df.columns.tolist(),
                'dtypes': df.dtypes.astype(str).to_dict(),
                'memory_usage': df.memory_usage(deep=True).sum(),
                'missing_values': df.isnull().sum().to_dict(),
                'preview': df.head(5).to_dict('records')
            }
            
            # Add column statistics for numeric columns
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) > 0:
                analysis['numeric_stats'] = df[numeric_columns].describe().to_dict()
            
            # Add categorical column info
            categorical_columns = df.select_dtypes(include=['object', 'category']).columns
            if len(categorical_columns) > 0:
                analysis['categorical_info'] = {}
                for col in categorical_columns:
                    analysis['categorical_info'][col] = {
                        'unique_values': df[col].nunique(),
                        'top_values': df[col].value_counts().head(5).to_dict()
                    }
            
            # Data quality assessment
            analysis['quality'] = {
                'completeness': (1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
                'duplicate_rows': df.duplicated().sum(),
                'empty_columns': (df.isnull().all()).sum()
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing DataFrame: {str(e)}")
            raise
    
    async def validate_data_source_connection(self, data_source: DataSource) -> Dict[str, Any]:
        """Validate connection to a data source"""
        try:
            if data_source.type == DataSourceType.DATABASE:
                return await self._validate_database_connection(data_source)
            elif data_source.type == DataSourceType.API:
                return await self._validate_api_connection(data_source)
            elif data_source.type == DataSourceType.FILE:
                return await self._validate_file_connection(data_source)
            else:
                return {
                    'status': 'error',
                    'message': f"Validation not implemented for type: {data_source.type}"
                }
                
        except Exception as e:
            logger.error(f"Error validating data source connection: {str(e)}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    async def _validate_database_connection(self, data_source: DataSource) -> Dict[str, Any]:
        """Validate database connection"""
        # TODO: Implement actual database connection testing
        # This would involve creating a connection using the connection_config
        return {
            'status': 'success',
            'message': 'Database connection validation not yet implemented',
            'connection_string': data_source.get_connection_string()
        }
    
    async def _validate_api_connection(self, data_source: DataSource) -> Dict[str, Any]:
        """Validate API connection"""
        # TODO: Implement actual API connection testing
        return {
            'status': 'success',
            'message': 'API connection validation not yet implemented'
        }
    
    async def _validate_file_connection(self, data_source: DataSource) -> Dict[str, Any]:
        """Validate file connection"""
        # TODO: Implement file path validation
        return {
            'status': 'success',
            'message': 'File connection validation not yet implemented'
        }


# Create singleton instance
data_processor = DataProcessor()
