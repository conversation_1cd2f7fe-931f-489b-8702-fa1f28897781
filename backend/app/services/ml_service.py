import asyncio
import logging
import pickle
import joblib
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import pandas as pd
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.ml_model import MLModel, ModelStatus, ModelType, MLFramework
from app.core.config import settings
from app.core.database import AsyncSessionLocal

logger = logging.getLogger(__name__)


class MLService:
    """Service for machine learning model training and management"""
    
    def __init__(self):
        self.model_storage_path = Path(settings.MODEL_STORAGE_PATH)
        self.model_storage_path.mkdir(parents=True, exist_ok=True)
    
    async def train_model(self, model_id: str, training_data: Dict[str, Any], config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Train a machine learning model"""
        async with AsyncSessionLocal() as db:
            try:
                # Get model
                model = await self._get_model(db, model_id)
                if not model:
                    raise ValueError(f"Model not found: {model_id}")
                
                # Check if model can be trained
                if not model.is_trainable:
                    raise ValueError(f"Model cannot be trained in current state: {model.status}")
                
                # Update model status
                model.status = ModelStatus.TRAINING
                model.training_start = datetime.utcnow().isoformat()
                await db.commit()
                
                # Perform training based on model type and framework
                result = await self._perform_training(model, training_data, config or {})
                
                # Update model with results
                model.training_end = datetime.utcnow().isoformat()
                
                if result['status'] == 'success':
                    model.status = ModelStatus.TRAINED
                    model.metrics = result.get('metrics', {})
                    model.validation_score = result.get('validation_score')
                    model.test_score = result.get('test_score')
                    model.model_path = result.get('model_path')
                    model.model_size_mb = result.get('model_size_mb')
                    model.training_samples = result.get('training_samples')
                    
                    # Calculate training duration
                    if model.training_start:
                        start_time = datetime.fromisoformat(model.training_start)
                        end_time = datetime.fromisoformat(model.training_end)
                        model.training_duration_seconds = int((end_time - start_time).total_seconds())
                else:
                    model.status = ModelStatus.FAILED
                
                await db.commit()
                
                return result
                
            except Exception as e:
                logger.error(f"Model training failed: {str(e)}")
                # Update model status on error
                if 'model' in locals():
                    model.status = ModelStatus.FAILED
                    await db.commit()
                
                return {
                    'status': 'error',
                    'error': str(e),
                    'model_id': model_id
                }
    
    async def predict(self, model_id: str, input_data: Dict[str, Any], return_probabilities: bool = False) -> Dict[str, Any]:
        """Make predictions with a trained model"""
        async with AsyncSessionLocal() as db:
            try:
                # Get model
                model = await self._get_model(db, model_id)
                if not model:
                    raise ValueError(f"Model not found: {model_id}")
                
                if not model.is_deployed:
                    raise ValueError(f"Model is not deployed: {model.status}")
                
                # Load model and make prediction
                result = await self._make_prediction(model, input_data, return_probabilities)
                
                # Update model statistics
                model.prediction_count += 1
                model.last_prediction = datetime.utcnow().isoformat()
                await db.commit()
                
                return result
                
            except Exception as e:
                logger.error(f"Prediction failed: {str(e)}")
                return {
                    'status': 'error',
                    'error': str(e),
                    'model_id': model_id
                }
    
    async def deploy_model(self, model_id: str, deployment_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Deploy a trained model for inference"""
        async with AsyncSessionLocal() as db:
            try:
                # Get model
                model = await self._get_model(db, model_id)
                if not model:
                    raise ValueError(f"Model not found: {model_id}")
                
                if model.status != ModelStatus.TRAINED:
                    raise ValueError(f"Only trained models can be deployed: {model.status}")
                
                # Perform deployment
                result = await self._deploy_model(model, deployment_config or {})
                
                if result['status'] == 'success':
                    model.status = ModelStatus.DEPLOYED
                    model.deployment_config = deployment_config
                    model.endpoint_url = result.get('endpoint_url')
                    model.deployment_status = 'active'
                
                await db.commit()
                
                return result
                
            except Exception as e:
                logger.error(f"Model deployment failed: {str(e)}")
                return {
                    'status': 'error',
                    'error': str(e),
                    'model_id': model_id
                }
    
    async def _perform_training(self, model: MLModel, training_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Perform the actual model training"""
        try:
            # Simulate training based on model type and framework
            if model.framework == MLFramework.SCIKIT_LEARN:
                return await self._train_sklearn_model(model, training_data, config)
            elif model.framework == MLFramework.AUTOGLUON:
                return await self._train_autogluon_model(model, training_data, config)
            elif model.framework == MLFramework.PYTORCH:
                return await self._train_pytorch_model(model, training_data, config)
            elif model.framework == MLFramework.TENSORFLOW:
                return await self._train_tensorflow_model(model, training_data, config)
            else:
                return await self._train_generic_model(model, training_data, config)
                
        except Exception as e:
            logger.error(f"Training failed for model {model.id}: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _train_sklearn_model(self, model: MLModel, training_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Train a scikit-learn model"""
        try:
            # Simulate scikit-learn training
            await asyncio.sleep(2)  # Simulate training time
            
            # Generate mock results
            model_path = self.model_storage_path / f"{model.id}_sklearn_model.joblib"
            
            # Create a dummy model file
            dummy_model = {'type': 'sklearn', 'algorithm': model.algorithm, 'trained': True}
            joblib.dump(dummy_model, model_path)
            
            return {
                'status': 'success',
                'message': 'Scikit-learn model trained successfully',
                'model_path': str(model_path),
                'model_size_mb': model_path.stat().st_size / (1024 * 1024),
                'metrics': {
                    'accuracy': 0.85,
                    'precision': 0.83,
                    'recall': 0.87,
                    'f1_score': 0.85
                },
                'validation_score': 0.85,
                'test_score': 0.83,
                'training_samples': training_data.get('sample_count', 1000)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _train_autogluon_model(self, model: MLModel, training_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Train an AutoGluon model"""
        try:
            # Simulate AutoGluon training
            await asyncio.sleep(3)  # Simulate training time
            
            model_path = self.model_storage_path / f"{model.id}_autogluon_model"
            model_path.mkdir(exist_ok=True)
            
            # Create dummy model files
            (model_path / "model.pkl").write_text("AutoGluon model placeholder")
            
            return {
                'status': 'success',
                'message': 'AutoGluon model trained successfully',
                'model_path': str(model_path),
                'model_size_mb': 15.5,
                'metrics': {
                    'accuracy': 0.92,
                    'precision': 0.91,
                    'recall': 0.93,
                    'f1_score': 0.92,
                    'roc_auc': 0.94
                },
                'validation_score': 0.92,
                'test_score': 0.90,
                'training_samples': training_data.get('sample_count', 1000)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _train_pytorch_model(self, model: MLModel, training_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Train a PyTorch model"""
        try:
            # Simulate PyTorch training
            await asyncio.sleep(5)  # Simulate training time
            
            model_path = self.model_storage_path / f"{model.id}_pytorch_model.pth"
            
            # Create dummy model file
            dummy_model = {'type': 'pytorch', 'architecture': model.algorithm, 'trained': True}
            with open(model_path, 'wb') as f:
                pickle.dump(dummy_model, f)
            
            return {
                'status': 'success',
                'message': 'PyTorch model trained successfully',
                'model_path': str(model_path),
                'model_size_mb': model_path.stat().st_size / (1024 * 1024),
                'metrics': {
                    'loss': 0.15,
                    'accuracy': 0.88,
                    'val_loss': 0.18,
                    'val_accuracy': 0.86
                },
                'validation_score': 0.86,
                'test_score': 0.84,
                'training_samples': training_data.get('sample_count', 1000)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _train_tensorflow_model(self, model: MLModel, training_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Train a TensorFlow model"""
        try:
            # Simulate TensorFlow training
            await asyncio.sleep(4)  # Simulate training time
            
            model_path = self.model_storage_path / f"{model.id}_tensorflow_model"
            model_path.mkdir(exist_ok=True)
            
            # Create dummy model files
            (model_path / "saved_model.pb").write_text("TensorFlow model placeholder")
            
            return {
                'status': 'success',
                'message': 'TensorFlow model trained successfully',
                'model_path': str(model_path),
                'model_size_mb': 25.3,
                'metrics': {
                    'loss': 0.12,
                    'accuracy': 0.89,
                    'val_loss': 0.16,
                    'val_accuracy': 0.87,
                    'auc': 0.93
                },
                'validation_score': 0.87,
                'test_score': 0.85,
                'training_samples': training_data.get('sample_count', 1000)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _train_generic_model(self, model: MLModel, training_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Train a generic model"""
        try:
            # Simulate generic training
            await asyncio.sleep(1)  # Simulate training time
            
            model_path = self.model_storage_path / f"{model.id}_generic_model.pkl"
            
            # Create dummy model file
            dummy_model = {'type': 'generic', 'framework': model.framework, 'trained': True}
            with open(model_path, 'wb') as f:
                pickle.dump(dummy_model, f)
            
            return {
                'status': 'success',
                'message': 'Generic model trained successfully',
                'model_path': str(model_path),
                'model_size_mb': model_path.stat().st_size / (1024 * 1024),
                'metrics': {
                    'score': 0.80
                },
                'validation_score': 0.80,
                'test_score': 0.78,
                'training_samples': training_data.get('sample_count', 1000)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _make_prediction(self, model: MLModel, input_data: Dict[str, Any], return_probabilities: bool) -> Dict[str, Any]:
        """Make prediction with a trained model"""
        try:
            # Simulate prediction
            await asyncio.sleep(0.1)  # Simulate inference time
            
            # Generate mock prediction based on model type
            if model.model_type == ModelType.CLASSIFICATION:
                prediction = "class_1"
                probabilities = {"class_1": 0.75, "class_2": 0.25} if return_probabilities else None
            elif model.model_type == ModelType.REGRESSION:
                prediction = 42.5
                probabilities = None
            else:
                prediction = "prediction_result"
                probabilities = None
            
            return {
                'status': 'success',
                'prediction': prediction,
                'probabilities': probabilities,
                'model_id': model.id,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _deploy_model(self, model: MLModel, deployment_config: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy a model for inference"""
        try:
            # Simulate deployment
            await asyncio.sleep(1)  # Simulate deployment time
            
            endpoint_url = f"/api/v1/ml/models/{model.id}/predict"
            
            return {
                'status': 'success',
                'message': 'Model deployed successfully',
                'endpoint_url': endpoint_url,
                'deployment_config': deployment_config
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _get_model(self, db: AsyncSession, model_id: str) -> Optional[MLModel]:
        """Get model by ID"""
        from sqlalchemy import select
        result = await db.execute(select(MLModel).where(MLModel.id == model_id))
        return result.scalar_one_or_none()


# Create singleton instance
ml_service = MLService()
