import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.pipeline import <PERSON>pel<PERSON>, PipelineStatus
from app.models.workflow import Workflow, WorkflowStatus
from app.core.database import AsyncSessionLocal

logger = logging.getLogger(__name__)


class PipelineExecutor:
    """Service for executing data pipelines and workflows"""
    
    def __init__(self):
        self.execution_context = {}
    
    async def execute_pipeline(self, pipeline_id: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a data pipeline"""
        async with AsyncSessionLocal() as db:
            try:
                # Get pipeline
                pipeline = await self._get_pipeline(db, pipeline_id)
                if not pipeline:
                    raise ValueError(f"Pipeline not found: {pipeline_id}")
                
                # Check if pipeline can be executed
                if not pipeline.can_execute:
                    raise ValueError(f"Pipeline cannot be executed in current state: {pipeline.status}")
                
                # Update pipeline status
                pipeline.status = PipelineStatus.ACTIVE
                pipeline.last_run_start = datetime.utcnow().isoformat()
                await db.commit()
                
                # Execute pipeline steps
                result = await self._execute_pipeline_steps(pipeline, config or {})
                
                # Update pipeline with results
                pipeline.last_run_end = datetime.utcnow().isoformat()
                pipeline.total_runs += 1
                
                if result['status'] == 'success':
                    pipeline.status = PipelineStatus.COMPLETED
                    pipeline.successful_runs += 1
                    pipeline.last_run_status = 'completed'
                else:
                    pipeline.status = PipelineStatus.FAILED
                    pipeline.failed_runs += 1
                    pipeline.last_run_status = 'failed'
                    pipeline.last_error = result.get('error', 'Unknown error')
                
                await db.commit()
                
                return result
                
            except Exception as e:
                logger.error(f"Pipeline execution failed: {str(e)}")
                # Update pipeline status on error
                if 'pipeline' in locals():
                    pipeline.status = PipelineStatus.FAILED
                    pipeline.last_run_status = 'failed'
                    pipeline.last_error = str(e)
                    pipeline.failed_runs += 1
                    await db.commit()
                
                return {
                    'status': 'error',
                    'error': str(e),
                    'pipeline_id': pipeline_id
                }
    
    async def execute_workflow(self, workflow_id: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a workflow"""
        async with AsyncSessionLocal() as db:
            try:
                # Get workflow
                workflow = await self._get_workflow(db, workflow_id)
                if not workflow:
                    raise ValueError(f"Workflow not found: {workflow_id}")
                
                # Check if workflow can be executed
                if not workflow.can_execute:
                    raise ValueError(f"Workflow cannot be executed in current state: {workflow.status}")
                
                # Update workflow status
                workflow.status = WorkflowStatus.ACTIVE
                workflow.last_execution_start = datetime.utcnow().isoformat()
                await db.commit()
                
                # Execute workflow nodes
                result = await self._execute_workflow_nodes(workflow, config or {})
                
                # Update workflow with results
                workflow.last_execution_end = datetime.utcnow().isoformat()
                workflow.total_executions += 1
                
                if result['status'] == 'success':
                    workflow.status = WorkflowStatus.COMPLETED
                    workflow.successful_executions += 1
                    workflow.last_execution_status = 'completed'
                else:
                    workflow.status = WorkflowStatus.FAILED
                    workflow.failed_executions += 1
                    workflow.last_execution_status = 'failed'
                    workflow.last_error = result.get('error', 'Unknown error')
                
                await db.commit()
                
                return result
                
            except Exception as e:
                logger.error(f"Workflow execution failed: {str(e)}")
                # Update workflow status on error
                if 'workflow' in locals():
                    workflow.status = WorkflowStatus.FAILED
                    workflow.last_execution_status = 'failed'
                    workflow.last_error = str(e)
                    workflow.failed_executions += 1
                    await db.commit()
                
                return {
                    'status': 'error',
                    'error': str(e),
                    'workflow_id': workflow_id
                }
    
    async def _execute_pipeline_steps(self, pipeline: Pipeline, config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute individual pipeline steps"""
        try:
            steps = pipeline.definition.get('steps', [])
            results = []
            
            for i, step in enumerate(steps):
                logger.info(f"Executing pipeline step {i+1}/{len(steps)}: {step.get('name', 'Unnamed')}")
                
                step_result = await self._execute_step(step, config)
                results.append(step_result)
                
                if step_result['status'] != 'success':
                    return {
                        'status': 'error',
                        'error': f"Step {i+1} failed: {step_result.get('error', 'Unknown error')}",
                        'step_results': results
                    }
            
            return {
                'status': 'success',
                'message': f"Pipeline executed successfully with {len(steps)} steps",
                'step_results': results
            }
            
        except Exception as e:
            logger.error(f"Error executing pipeline steps: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _execute_workflow_nodes(self, workflow: Workflow, config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute workflow nodes in dependency order"""
        try:
            nodes = workflow.nodes or []
            edges = workflow.edges or []
            
            # Build dependency graph
            dependencies = self._build_dependency_graph(nodes, edges)
            
            # Execute nodes in topological order
            execution_order = self._topological_sort(dependencies)
            results = {}
            
            for node_id in execution_order:
                node = next((n for n in nodes if n.get('id') == node_id), None)
                if not node:
                    continue
                
                logger.info(f"Executing workflow node: {node.get('name', node_id)}")
                
                # Get input data from previous nodes
                input_data = self._get_node_inputs(node_id, edges, results)
                
                # Execute node
                node_result = await self._execute_node(node, input_data, config)
                results[node_id] = node_result
                
                if node_result['status'] != 'success':
                    return {
                        'status': 'error',
                        'error': f"Node {node_id} failed: {node_result.get('error', 'Unknown error')}",
                        'node_results': results
                    }
            
            return {
                'status': 'success',
                'message': f"Workflow executed successfully with {len(nodes)} nodes",
                'node_results': results
            }
            
        except Exception as e:
            logger.error(f"Error executing workflow nodes: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _execute_step(self, step: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single pipeline step"""
        try:
            step_type = step.get('type', 'unknown')
            
            # Simulate step execution based on type
            if step_type == 'data_source':
                return await self._execute_data_source_step(step, config)
            elif step_type == 'transform':
                return await self._execute_transform_step(step, config)
            elif step_type == 'output':
                return await self._execute_output_step(step, config)
            else:
                return {
                    'status': 'success',
                    'message': f"Simulated execution of {step_type} step",
                    'data': {'step_type': step_type, 'config': step.get('config', {})}
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _execute_node(self, node: Dict[str, Any], input_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow node"""
        try:
            node_type = node.get('type', 'unknown')
            
            # Simulate node execution based on type
            await asyncio.sleep(0.1)  # Simulate processing time
            
            return {
                'status': 'success',
                'message': f"Executed {node_type} node",
                'output_data': {
                    'node_type': node_type,
                    'input_data': input_data,
                    'config': node.get('config', {})
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def _execute_data_source_step(self, step: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute data source step"""
        # TODO: Implement actual data source reading
        return {
            'status': 'success',
            'message': 'Data source step executed',
            'data': {'rows': 1000, 'columns': 10}
        }
    
    async def _execute_transform_step(self, step: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute transform step"""
        # TODO: Implement actual data transformation
        return {
            'status': 'success',
            'message': 'Transform step executed',
            'data': {'transformed_rows': 950, 'columns': 12}
        }
    
    async def _execute_output_step(self, step: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute output step"""
        # TODO: Implement actual data output
        return {
            'status': 'success',
            'message': 'Output step executed',
            'data': {'output_location': '/data/processed/output.csv'}
        }
    
    def _build_dependency_graph(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Build dependency graph from nodes and edges"""
        dependencies = {node.get('id'): [] for node in nodes}
        
        for edge in edges:
            target = edge.get('target')
            source = edge.get('source')
            if target and source:
                dependencies[target].append(source)
        
        return dependencies
    
    def _topological_sort(self, dependencies: Dict[str, List[str]]) -> List[str]:
        """Perform topological sort to determine execution order"""
        # Simple topological sort implementation
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(node):
            if node in temp_visited:
                raise ValueError("Circular dependency detected")
            if node in visited:
                return
            
            temp_visited.add(node)
            for dep in dependencies.get(node, []):
                visit(dep)
            temp_visited.remove(node)
            visited.add(node)
            result.append(node)
        
        for node in dependencies:
            if node not in visited:
                visit(node)
        
        return result
    
    def _get_node_inputs(self, node_id: str, edges: List[Dict[str, Any]], results: Dict[str, Any]) -> Dict[str, Any]:
        """Get input data for a node from previous node results"""
        inputs = {}
        
        for edge in edges:
            if edge.get('target') == node_id:
                source_id = edge.get('source')
                if source_id in results:
                    inputs[source_id] = results[source_id].get('output_data', {})
        
        return inputs
    
    async def _get_pipeline(self, db: AsyncSession, pipeline_id: str) -> Optional[Pipeline]:
        """Get pipeline by ID"""
        from sqlalchemy import select
        result = await db.execute(select(Pipeline).where(Pipeline.id == pipeline_id))
        return result.scalar_one_or_none()
    
    async def _get_workflow(self, db: AsyncSession, workflow_id: str) -> Optional[Workflow]:
        """Get workflow by ID"""
        from sqlalchemy import select
        result = await db.execute(select(Workflow).where(Workflow.id == workflow_id))
        return result.scalar_one_or_none()


# Create singleton instance
pipeline_executor = PipelineExecutor()
