from celery import Celery
from app.core.config import settings

# Create Celery app
celery_app = Celery(
    "ai_platform",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    beat_schedule={
        # Add scheduled tasks here
        "health-check": {
            "task": "app.workers.tasks.health_check",
            "schedule": 60.0,  # Every minute
        },
    },
)

# Auto-discover tasks
celery_app.autodiscover_tasks(["app.workers"])
