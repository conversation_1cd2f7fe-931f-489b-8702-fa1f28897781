[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-data-platform"
version = "1.0.0"
description = "Enterprise AI Data Platform with Multi-Cloud ML Pipeline"
authors = [
    {name = "AI Data Platform Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.23.0",
    "sqlalchemy>=2.0.0",
    "asyncpg>=0.30.0",
    "alembic>=1.16.0",
    "pydantic>=2.0.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "aiofiles>=24.1.0",
    "aioredis>=2.0.1",
    "structlog>=23.1.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "pyotp>=2.8.0",
    "qrcode[pil]>=7.4.0",
    "cryptography>=41.0.0",
    "httpx>=0.24.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
]

[project.optional-dependencies]
ml = [
    "torch>=2.0.0",
    "tensorflow>=2.13.0",
    "autogluon>=0.8.0",
    "xgboost>=1.7.0",
    "lightgbm>=4.0.0",
    "mlflow>=2.5.0",
]

cloud = [
    "boto3>=1.28.0",
    "sagemaker>=2.175.0",
    "azure-ai-ml>=1.8.0",
    "azure-identity>=1.13.0",
    "google-cloud-aiplatform>=1.29.0",
]

dev = [
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
    "pytest-cov>=4.1.0",
    "pre-commit>=3.3.0",
]

[project.urls]
Homepage = "https://github.com/ai-data-platform/ai-data-platform"
Documentation = "https://docs.aidataplatform.com"
Repository = "https://github.com/ai-data-platform/ai-data-platform.git"
Issues = "https://github.com/ai-data-platform/ai-data-platform/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100
known_first_party = ["app"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "autogluon.*",
    "sagemaker.*",
    "azure.*",
    "google.*",
    "mlflow.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
