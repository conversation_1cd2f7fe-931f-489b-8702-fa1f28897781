# ML and Cloud Provider Dependencies for AI Data Platform

# Core ML Libraries
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# Deep Learning Frameworks
torch>=2.0.0
torchvision>=0.15.0
tensorflow>=2.13.0
keras>=2.13.0

# AutoML Libraries
autogluon>=0.8.0
auto-sklearn>=0.15.0
h2o>=3.42.0

# Gradient Boosting
xgboost>=1.7.0
lightgbm>=4.0.0
catboost>=1.2.0

# MLOps and Experiment Tracking
mlflow>=2.5.0
wandb>=0.15.0
neptune-client>=1.0.0

# Distributed Computing
ray[default]>=2.5.0
dask[complete]>=2023.6.0

# Cloud Provider SDKs
# AWS
boto3>=1.28.0
sagemaker>=2.175.0
awscli>=1.29.0

# Azure
azure-ai-ml>=1.8.0
azure-identity>=1.13.0
azure-storage-blob>=12.17.0
azure-keyvault-secrets>=4.7.0

# Google Cloud
google-cloud-aiplatform>=1.29.0
google-cloud-storage>=2.10.0
google-cloud-bigquery>=3.11.0
google-auth>=2.22.0

# Data Processing
pyarrow>=12.0.0
fastparquet>=2023.4.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# Feature Engineering
feature-engine>=1.6.0
category-encoders>=2.6.0
imbalanced-learn>=0.11.0

# Model Interpretation
shap>=0.42.0
lime>=0.2.0
eli5>=0.13.0

# Hyperparameter Optimization
optuna>=3.2.0
hyperopt>=0.2.7
scikit-optimize>=0.9.0

# Time Series
prophet>=1.1.0
statsmodels>=0.14.0
pmdarima>=2.0.0

# Computer Vision
opencv-python>=4.8.0
pillow>=10.0.0
albumentations>=1.3.0

# Natural Language Processing
transformers>=4.30.0
tokenizers>=0.13.0
datasets>=2.13.0
spacy>=3.6.0
nltk>=3.8.0

# Model Serving
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
gunicorn>=21.0.0
celery>=5.3.0
redis>=4.6.0

# Monitoring and Observability
prometheus-client>=0.17.0
structlog>=23.1.0
sentry-sdk>=1.28.0

# Security
cryptography>=41.0.0
pyotp>=2.8.0
qrcode[pil]>=7.4.0

# Utilities
tqdm>=4.65.0
joblib>=1.3.0
psutil>=5.9.0
requests>=2.31.0
aiofiles>=23.1.0
python-multipart>=0.0.6

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.4.0

# Jupyter and Notebooks (for development)
jupyter>=1.0.0
jupyterlab>=4.0.0
ipywidgets>=8.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
