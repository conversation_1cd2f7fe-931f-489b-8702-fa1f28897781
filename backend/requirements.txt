absl-py==2.3.0
accelerate==1.8.1
adagio==0.2.6
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiohttp-cors==0.8.1
aioredis==2.0.1
aiosignal==1.3.2
aiosqlite==0.21.0
alembic==1.16.2
amqp==5.3.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
appdirs==1.4.4
apscheduler==3.11.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
astroid==3.3.10
async-timeout==5.0.1
asyncpg==0.30.0
attrs==25.3.0
authlib==1.6.0
autogluon==1.3.1
autogluon-common==1.3.1
autogluon-core==1.3.1
autogluon-features==1.3.1
autogluon-multimodal==1.3.1
autogluon-tabular==1.3.1
autogluon-timeseries==1.3.1
banks==2.1.3
bcrypt==4.3.0
beartype==0.21.0
beautifulsoup4==4.13.4
billiard==4.2.1
black==25.1.0
blinker==1.9.0
blis==1.3.0
boto3==1.38.46
botocore==1.38.46
cachetools==5.5.2
catalogue==2.0.10
catboost==1.2.8
celery==5.5.3
certifi==2025.6.15
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1.2
click-repl==0.3.0
cloudpathlib==0.21.1
cloudpickle==3.1.1
colorama==0.4.6
colorful==0.5.6
colorlog==6.9.0
confection==0.1.5
contourpy==1.3.2
coreforecast==0.0.15
coverage==7.9.1
cramjam==2.10.0
cryptography==45.0.4
cycler==0.12.1
cymem==2.0.11
databricks-sdk==0.57.0
dataclasses-json==0.6.7
datasets==2.14.4
defusedxml==0.7.1
deprecated==1.2.18
dill==0.3.7
dirtyjson==1.0.8
distlib==0.3.9
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
ecdsa==0.19.1
einops==0.8.1
email-validator==2.2.0
environs==14.2.0
et-xmlfile==2.0.0
evaluate==0.4.4
faker==37.4.0
fastai==2.8.2
fastapi==0.115.14
fastapi-cors==0.0.6
fastapi-users==14.0.1
fastcore==1.8.4
fastdownload==0.0.7
fastparquet==2024.11.0
fastprogress==1.0.3
fasttransform==0.0.2
filelock==3.18.0
filetype==1.2.0
flake8==7.3.0
flask==3.1.1
fonttools==4.58.4
frozenlist==1.7.0
fs==2.4.16
fsspec==2025.5.1
fugue==0.9.1
future==1.0.0
gdown==5.2.0
gitdb==4.0.12
gitpython==3.1.44
gluonts==0.16.2
google-api-core==2.25.1
google-auth==2.40.3
googleapis-common-protos==1.70.0
graphene==3.4.3
graphql-core==3.2.6
graphql-relay==3.2.0
graphviz==0.21
greenlet==3.2.3
griffe==1.7.3
grpcio==1.73.1
gunicorn==23.0.0
h11==0.16.0

# Security packages
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
slowapi==0.1.9
cryptography==41.0.7

# Monitoring & Observability
sentry-sdk[fastapi]==1.38.0
prometheus-client==0.19.0
structlog==23.2.0
h2==4.2.0
hf-xet==1.1.5
hpack==4.1.0
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.33.1
hyperframe==6.1.0
hyperopt==0.2.7
identify==2.6.12
idna==3.10
imageio==2.37.0
importlib-metadata==8.7.0
iniconfig==2.1.0
isort==6.0.1
itsdangerous==2.2.0
jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
kombu==5.5.4
langchain==0.3.26
langchain-core==0.3.66
langchain-text-splitters==0.3.8
langcodes==3.5.0
langgraph==0.5.0
langgraph-checkpoint==2.1.0
langgraph-prebuilt==0.5.1
langgraph-sdk==0.1.72
langsmith==0.4.4
language-data==1.3.0
lazy-loader==0.4
lightgbm==4.6.0
lightning==2.5.2
lightning-utilities==0.14.3
llama-cloud==0.1.26
llama-cloud-services==0.6.34
llama-index==0.12.44
llama-index-agent-openai==0.4.12
llama-index-cli==0.4.3
llama-index-core==0.12.44
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.7.7
llama-index-instrumentation==0.2.0
llama-index-llms-openai==0.4.7
llama-index-multi-modal-llms-openai==0.5.1
llama-index-program-openai==0.3.2
llama-index-question-gen-openai==0.3.1
llama-index-readers-file==0.4.9
llama-index-readers-llama-parse==0.4.0
llama-index-workflows==1.0.1
llama-parse==0.6.34
llvmlite==0.44.0
loguru==0.7.3
makefun==1.16.0
mako==1.3.10
marisa-trie==1.2.1
markdown==3.8.2
markdown-it-py==3.0.0
markupsafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
mccabe==0.7.0
mdurl==0.1.2
mlflow==3.1.1
mlflow-skinny==3.1.1
mlforecast==0.13.6
model-index==0.1.11
mpmath==1.3.0
msgpack==1.1.1
multidict==6.6.2
multiprocess==0.70.15
murmurhash==1.0.13
mypy==1.16.1
mypy-extensions==1.1.0
narwhals==1.44.0
nest-asyncio==1.6.0
networkx==3.5
nlpaug==1.1.11
nltk==3.8.1
nodeenv==1.9.1
numba==0.61.2
numpy==2.1.3
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py3==7.352.0
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
omegaconf==2.3.0
openai==1.93.0
opencensus==0.11.4
opencensus-context==0.1.3
opendatalab==0.0.10
openmim==0.3.9
openpyxl==3.1.5
opentelemetry-api==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
openxlab==0.0.11
optuna==4.4.0
ordered-set==4.1.0
orjson==3.10.18
ormsgpack==1.10.0
packaging==24.2
pandas==2.2.3
passlib==1.7.4
pathspec==0.12.1
patsy==1.0.1
pdf2image==1.17.0
pillow==11.2.1
pip==24.0
platformdirs==4.3.8
plotly==6.2.0
pluggy==1.6.0
plum-dispatch==2.5.7
portalocker==2.10.1
pre-commit==4.2.0
preshed==3.0.10
prometheus-client==0.22.1
prompt-toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==6.31.1
psutil==7.0.0
pwdlib==0.2.1
py-spy==0.4.0
py4j==********
pyarrow==20.0.0
pyasn1==0.6.1
pyasn1-modules==0.4.2
pycodestyle==2.14.0
pycparser==2.22
pycryptodome==3.23.0
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
pyflakes==3.4.0
pygments==2.19.2
pyjwt==2.10.1
pylint==3.3.7
pyparsing==3.2.3
pypdf==5.6.1
pysocks==1.7.1
pytesseract==0.3.13
pytest==8.4.1
pytest-asyncio==1.0.0
pytest-cov==6.2.1
pytest-mock==3.14.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-jose==3.5.0
python-multipart==0.0.20
pytorch-lightning==2.5.2
pytorch-metric-learning==2.8.1
pytz==2025.2
pyyaml==6.0.2
qdrant-client==1.14.3
ray==2.44.1
redis==6.2.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-toolbelt==1.0.0
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
s3transfer==0.13.0
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.3
sentence-transformers==4.1.0
sentencepiece==0.2.0
sentry-sdk==2.32.0
seqeval==1.2.2
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.7
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
sqladmin==0.20.1
sqlalchemy==2.0.41
sqlparse==0.5.3
srsly==2.5.1
starlette==0.46.2
statsforecast==2.0.1
statsmodels==0.14.4
striprtf==0.0.26
sympy==1.13.1
tabulate==0.9.0
tenacity==9.1.2
tensorboard==2.19.0
tensorboard-data-server==0.7.2
tensorboardx==2.6.4
text-unidecode==1.3
thinc==8.3.6
threadpoolctl==3.6.0
tifffile==2025.6.11
tiktoken==0.9.0
timm==1.0.3
tokenizers==0.21.2
tomlkit==0.13.3
toolz==0.12.1
torch==2.6.0
torchmetrics==1.7.3
torchvision==0.21.0
tqdm==4.67.1
transformers==4.49.0
triad==0.9.8
triton==3.2.0
typer==0.16.0
types-cffi==1.17.0.20250523
types-pyopenssl==24.1.0.20240722
types-redis==4.6.0.20241004
types-requests==2.32.4.20250611
types-setuptools==80.9.0.20250529
typing-extensions==4.14.0
typing-inspect==0.9.0
typing-inspection==0.4.1
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.5.0
utilsforecast==0.2.10
uv==0.7.16
uvicorn==0.35.0
vine==5.1.0
virtualenv==20.31.2
wasabi==1.1.3
wcwidth==0.2.13
weasel==0.4.1
werkzeug==3.1.3
window-ops==0.0.15
wrapt==1.17.2
wtforms==3.1.2
xgboost==3.0.2
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0
