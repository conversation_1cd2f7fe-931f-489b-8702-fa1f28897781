#!/usr/bin/env python3
"""
Database initialization script for AI Data Platform
Creates database, runs migrations, and seeds initial data
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from alembic.config import Config
from alembic import command
import structlog

from app.core.config import settings
from app.core.database import Base, get_async_session
from app.models.user import User, UserRole
from app.models.project import Project, ProjectType, ProjectStatus
from app.core.security import get_password_hash

logger = structlog.get_logger()

async def create_database_if_not_exists():
    """Create the database if it doesn't exist"""
    
    # Parse the database URL to get connection details
    db_url = str(settings.DATABASE_URL)
    
    # Create connection to postgres database (not the target database)
    postgres_url = db_url.replace(f"/{settings.DATABASE_NAME}", "/postgres")
    
    engine = create_async_engine(postgres_url, isolation_level="AUTOCOMMIT")
    
    try:
        async with engine.connect() as conn:
            # Check if database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": settings.DATABASE_NAME}
            )
            
            if not result.fetchone():
                logger.info(f"Creating database: {settings.DATABASE_NAME}")
                await conn.execute(
                    text(f'CREATE DATABASE "{settings.DATABASE_NAME}"')
                )
                logger.info(f"Database {settings.DATABASE_NAME} created successfully")
            else:
                logger.info(f"Database {settings.DATABASE_NAME} already exists")
                
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        raise
    finally:
        await engine.dispose()

async def run_migrations():
    """Run Alembic migrations"""
    
    logger.info("Running database migrations...")
    
    try:
        # Set up Alembic configuration
        alembic_cfg = Config(str(backend_dir / "alembic.ini"))
        alembic_cfg.set_main_option("sqlalchemy.url", str(settings.DATABASE_URL))
        
        # Run migrations
        command.upgrade(alembic_cfg, "head")
        logger.info("Migrations completed successfully")
        
    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        raise

async def create_initial_data():
    """Create initial data for the platform"""
    
    logger.info("Creating initial data...")
    
    try:
        async for session in get_async_session():
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                full_name="Platform Administrator",
                hashed_password=get_password_hash("admin123"),
                role=UserRole.ADMIN,
                is_active=True,
                is_verified=True
            )
            
            # Check if admin user already exists
            existing_admin = await session.get(User, admin_user.id)
            if not existing_admin:
                session.add(admin_user)
                logger.info("Created admin user")
            
            # Create demo user
            demo_user = User(
                email="<EMAIL>",
                username="demo",
                full_name="Demo User",
                hashed_password=get_password_hash("demo123"),
                role=UserRole.DATA_SCIENTIST,
                is_active=True,
                is_verified=True
            )
            
            existing_demo = await session.get(User, demo_user.id)
            if not existing_demo:
                session.add(demo_user)
                logger.info("Created demo user")
            
            # Create sample project
            sample_project = Project(
                name="Sample ML Project",
                description="A sample machine learning project for demonstration",
                project_type=ProjectType.DATA_SCIENCE,
                status=ProjectStatus.ACTIVE,
                owner_id=admin_user.id,
                settings={
                    "ml_framework": "scikit_learn",
                    "data_sources": ["sample_dataset.csv"],
                    "target_variable": "target",
                    "model_type": "classification"
                }
            )
            
            session.add(sample_project)
            logger.info("Created sample project")
            
            await session.commit()
            logger.info("Initial data created successfully")
            
    except Exception as e:
        logger.error(f"Error creating initial data: {e}")
        raise

async def verify_database_setup():
    """Verify that the database setup is working correctly"""
    
    logger.info("Verifying database setup...")
    
    try:
        async for session in get_async_session():
            # Test basic queries
            result = await session.execute(text("SELECT COUNT(*) FROM users"))
            user_count = result.scalar()
            logger.info(f"Found {user_count} users in database")
            
            result = await session.execute(text("SELECT COUNT(*) FROM projects"))
            project_count = result.scalar()
            logger.info(f"Found {project_count} projects in database")
            
            # Test that we can query with relationships
            from sqlalchemy import select
            stmt = select(User).where(User.role == UserRole.ADMIN)
            result = await session.execute(stmt)
            admin_users = result.scalars().all()
            logger.info(f"Found {len(admin_users)} admin users")
            
            logger.info("Database verification completed successfully")
            
    except Exception as e:
        logger.error(f"Error verifying database setup: {e}")
        raise

async def main():
    """Main initialization function"""
    
    logger.info("Starting database initialization...")
    
    try:
        # Step 1: Create database if it doesn't exist
        await create_database_if_not_exists()
        
        # Step 2: Run migrations
        await run_migrations()
        
        # Step 3: Create initial data
        await create_initial_data()
        
        # Step 4: Verify setup
        await verify_database_setup()
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
