#!/usr/bin/env python3
"""
Database connection test script for AI Data Platform
Tests database connectivity and basic operations
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import structlog

from app.core.config import settings
from app.core.database import get_async_session
from app.models.user import User, UserRole

logger = structlog.get_logger()

async def test_basic_connection():
    """Test basic database connection"""
    
    logger.info("Testing basic database connection...")
    
    try:
        engine = create_async_engine(str(settings.DATABASE_URL))
        
        async with engine.connect() as conn:
            result = await conn.execute(text("SELECT version()"))
            version = result.scalar()
            logger.info(f"Connected to PostgreSQL: {version}")
            
        await engine.dispose()
        return True
        
    except Exception as e:
        logger.error(f"Basic connection test failed: {e}")
        return False

async def test_session_management():
    """Test session management and basic queries"""
    
    logger.info("Testing session management...")
    
    try:
        async for session in get_async_session():
            # Test simple query
            result = await session.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            
            if test_value == 1:
                logger.info("Session management test passed")
                return True
            else:
                logger.error("Session management test failed: unexpected result")
                return False
                
    except Exception as e:
        logger.error(f"Session management test failed: {e}")
        return False

async def test_model_operations():
    """Test basic model operations (CRUD)"""
    
    logger.info("Testing model operations...")
    
    try:
        async for session in get_async_session():
            # Create a test user
            test_user = User(
                email="<EMAIL>",
                username="testuser",
                full_name="Test User",
                hashed_password="hashed_password_here",
                role=UserRole.VIEWER,
                is_active=True,
                is_verified=False
            )
            
            # Add and commit
            session.add(test_user)
            await session.commit()
            await session.refresh(test_user)
            
            logger.info(f"Created test user with ID: {test_user.id}")
            
            # Read the user back
            from sqlalchemy import select
            stmt = select(User).where(User.email == "<EMAIL>")
            result = await session.execute(stmt)
            retrieved_user = result.scalar_one_or_none()
            
            if retrieved_user:
                logger.info(f"Retrieved user: {retrieved_user.username}")
                
                # Update the user
                retrieved_user.full_name = "Updated Test User"
                await session.commit()
                
                logger.info("Updated user successfully")
                
                # Delete the user
                await session.delete(retrieved_user)
                await session.commit()
                
                logger.info("Deleted test user successfully")
                
                return True
            else:
                logger.error("Failed to retrieve test user")
                return False
                
    except Exception as e:
        logger.error(f"Model operations test failed: {e}")
        return False

async def test_relationships():
    """Test model relationships"""
    
    logger.info("Testing model relationships...")
    
    try:
        async for session in get_async_session():
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload
            from app.models.project import Project
            
            # Test loading user with projects
            stmt = select(User).options(selectinload(User.owned_projects)).limit(1)
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            
            if user:
                logger.info(f"User {user.username} has {len(user.owned_projects)} projects")
                return True
            else:
                logger.info("No users found for relationship test")
                return True  # This is okay for a fresh database
                
    except Exception as e:
        logger.error(f"Relationship test failed: {e}")
        return False

async def test_transactions():
    """Test transaction handling"""
    
    logger.info("Testing transaction handling...")
    
    try:
        async for session in get_async_session():
            # Test rollback
            test_user = User(
                email="<EMAIL>",
                username="rollbackuser",
                full_name="Rollback Test User",
                hashed_password="hashed_password_here",
                role=UserRole.VIEWER,
                is_active=True,
                is_verified=False
            )
            
            session.add(test_user)
            
            # Rollback without commit
            await session.rollback()
            
            # Check that user was not saved
            from sqlalchemy import select
            stmt = select(User).where(User.email == "<EMAIL>")
            result = await session.execute(stmt)
            retrieved_user = result.scalar_one_or_none()
            
            if retrieved_user is None:
                logger.info("Transaction rollback test passed")
                return True
            else:
                logger.error("Transaction rollback test failed: user was saved")
                return False
                
    except Exception as e:
        logger.error(f"Transaction test failed: {e}")
        return False

async def test_connection_pooling():
    """Test connection pooling"""
    
    logger.info("Testing connection pooling...")
    
    try:
        # Create multiple concurrent connections
        tasks = []
        
        async def concurrent_query(query_id):
            async for session in get_async_session():
                result = await session.execute(text(f"SELECT {query_id} as query_id"))
                return result.scalar()
        
        # Run 10 concurrent queries
        for i in range(10):
            tasks.append(concurrent_query(i))
        
        results = await asyncio.gather(*tasks)
        
        if len(results) == 10 and all(results[i] == i for i in range(10)):
            logger.info("Connection pooling test passed")
            return True
        else:
            logger.error("Connection pooling test failed")
            return False
            
    except Exception as e:
        logger.error(f"Connection pooling test failed: {e}")
        return False

async def run_all_tests():
    """Run all database tests"""
    
    logger.info("Starting comprehensive database tests...")
    
    tests = [
        ("Basic Connection", test_basic_connection),
        ("Session Management", test_session_management),
        ("Model Operations", test_model_operations),
        ("Relationships", test_relationships),
        ("Transactions", test_transactions),
        ("Connection Pooling", test_connection_pooling),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"Running {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name} test: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name} test: FAILED with exception: {e}")
    
    # Summary
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    logger.info(f"\nTest Summary: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("All database tests passed! ✅")
        return True
    else:
        logger.error("Some database tests failed! ❌")
        for test_name, result in results.items():
            status = "✅" if result else "❌"
            logger.info(f"  {status} {test_name}")
        return False

async def main():
    """Main test function"""
    
    try:
        success = await run_all_tests()
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"Test suite failed with exception: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
