"""
Basic tests for AI Data Platform
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from app.main import app

client = TestClient(app)

def test_health_endpoint():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_docs_endpoint():
    """Test API documentation endpoint"""
    response = client.get("/docs")
    assert response.status_code == 200

def test_openapi_endpoint():
    """Test OpenAPI schema endpoint"""
    response = client.get("/openapi.json")
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert "info" in data

def test_root_endpoint():
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200

def test_api_v1_prefix():
    """Test API v1 prefix is working"""
    response = client.get("/api/v1/")
    # Should return 404 for base path, but not 500
    assert response.status_code in [404, 405]

def test_cors_headers():
    """Test CORS headers are present"""
    response = client.get("/health")
    assert response.status_code == 200
    # Check if CORS headers would be present in a real request
    # This is a basic test - full CORS testing would need actual cross-origin requests

def test_security_headers():
    """Test security headers are present"""
    response = client.get("/health")
    assert response.status_code == 200
    # In a real deployment, we'd check for security headers
    # This is a placeholder for security header validation

class TestAPIStructure:
    """Test API structure and routing"""
    
    def test_api_routes_exist(self):
        """Test that main API routes are properly configured"""
        # Test that the app has the expected route structure
        routes = [route.path for route in app.routes]
        
        # Should have health endpoint
        assert "/health" in routes or any("/health" in route for route in routes)
        
        # Should have docs
        assert "/docs" in routes or any("/docs" in route for route in routes)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
