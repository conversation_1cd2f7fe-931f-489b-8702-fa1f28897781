"""
Comprehensive test suite for AI Data Platform
"""

import pytest
import asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from app.main import app
from app.core.database import get_db, engine
from app.models.user import User
from app.models.compliance import Consent<PERSON><PERSON><PERSON>, AuditLog
from app.core.auth import create_access_token
from app.core.compliance import gdpr_compliance


class TestAuthentication:
    """Test authentication and authorization"""
    
    @pytest.mark.asyncio
    async def test_user_registration(self, async_client: AsyncClient):
        """Test user registration"""
        response = await async_client.post("/api/v1/auth/register", json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "full_name": "Test User"
        })
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == "testuser"
        assert data["email"] == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_user_login(self, async_client: AsyncClient):
        """Test user login"""
        # First register a user
        await async_client.post("/api/v1/auth/register", json={
            "username": "logintest",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "full_name": "Login Test"
        })
        
        # Then login
        response = await async_client.post("/api/v1/auth/login", json={
            "username": "logintest",
            "password": "TestPassword123!"
        })
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
    
    @pytest.mark.asyncio
    async def test_protected_endpoint(self, async_client: AsyncClient, auth_headers):
        """Test accessing protected endpoint"""
        response = await async_client.get("/api/v1/auth/me", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "username" in data


class TestMLPipeline:
    """Test ML pipeline functionality"""
    
    @pytest.mark.asyncio
    async def test_automl_training(self, async_client: AsyncClient, auth_headers):
        """Test AutoML training endpoint"""
        response = await async_client.post("/api/v1/ml/automl/train", 
            headers=auth_headers,
            json={
                "name": "Test Model",
                "dataset_id": "test-dataset",
                "target_column": "target",
                "model_type": "classification",
                "time_budget_hours": 1
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "job_id" in data
        assert "model_id" in data
    
    @pytest.mark.asyncio
    async def test_model_list(self, async_client: AsyncClient, auth_headers):
        """Test listing ML models"""
        response = await async_client.get("/api/v1/ml/models", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert isinstance(data["items"], list)


class TestDataManagement:
    """Test data management functionality"""
    
    @pytest.mark.asyncio
    async def test_file_upload(self, async_client: AsyncClient, auth_headers):
        """Test file upload"""
        # Create a test CSV file
        test_file_content = "name,age,city\nJohn,25,NYC\nJane,30,LA"
        
        response = await async_client.post("/api/v1/data/upload",
            headers=auth_headers,
            files={"file": ("test.csv", test_file_content, "text/csv")}
        )
        assert response.status_code == 200
        data = response.json()
        assert "filename" in data
        assert "file_size" in data
    
    @pytest.mark.asyncio
    async def test_data_sources_list(self, async_client: AsyncClient, auth_headers):
        """Test listing data sources"""
        response = await async_client.get("/api/v1/data/sources", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "items" in data


class TestCompliance:
    """Test GDPR compliance functionality"""
    
    @pytest.mark.asyncio
    async def test_consent_recording(self, async_client: AsyncClient, auth_headers):
        """Test recording user consent"""
        response = await async_client.post("/api/v1/compliance/consent",
            headers=auth_headers,
            json={
                "purpose": "data_processing",
                "consent_given": True,
                "consent_method": "web_form",
                "consent_evidence": "User clicked agree button"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "consent_id" in data
        assert data["status"] == "recorded"
    
    @pytest.mark.asyncio
    async def test_data_subject_request(self, async_client: AsyncClient, auth_headers):
        """Test data subject request creation"""
        response = await async_client.post("/api/v1/compliance/data-subject-request",
            headers=auth_headers,
            json={
                "request_type": "access",
                "request_details": {"reason": "I want to see my data"}
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "request_id" in data
        assert data["status"] == "pending"
    
    @pytest.mark.asyncio
    async def test_data_export(self, async_client: AsyncClient, auth_headers):
        """Test user data export"""
        response = await async_client.get("/api/v1/compliance/data-export", 
                                        headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "export_date" in data
        assert "data" in data


class TestSecurity:
    """Test security features"""
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, async_client: AsyncClient):
        """Test rate limiting on login endpoint"""
        # Make multiple rapid requests
        responses = []
        for _ in range(10):
            response = await async_client.post("/api/v1/auth/login", json={
                "username": "nonexistent",
                "password": "wrong"
            })
            responses.append(response.status_code)
        
        # Should eventually get rate limited
        assert 429 in responses or 401 in responses
    
    @pytest.mark.asyncio
    async def test_sql_injection_protection(self, async_client: AsyncClient, auth_headers):
        """Test SQL injection protection"""
        # Try SQL injection in search parameter
        response = await async_client.get("/api/v1/data/sources?search='; DROP TABLE users; --",
                                        headers=auth_headers)
        # Should not crash and return normal response
        assert response.status_code in [200, 400, 422]
    
    @pytest.mark.asyncio
    async def test_xss_protection(self, async_client: AsyncClient, auth_headers):
        """Test XSS protection"""
        # Try XSS in user input
        response = await async_client.post("/api/v1/compliance/consent",
            headers=auth_headers,
            json={
                "purpose": "<script>alert('xss')</script>",
                "consent_given": True,
                "consent_method": "web_form",
                "consent_evidence": "Test"
            }
        )
        # Should either sanitize or reject
        assert response.status_code in [200, 400, 422]


class TestPerformance:
    """Test performance and scalability"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, async_client: AsyncClient, auth_headers):
        """Test handling concurrent requests"""
        async def make_request():
            return await async_client.get("/api/v1/auth/me", headers=auth_headers)
        
        # Make 10 concurrent requests
        tasks = [make_request() for _ in range(10)]
        responses = await asyncio.gather(*tasks)
        
        # All should succeed
        for response in responses:
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_large_file_upload(self, async_client: AsyncClient, auth_headers):
        """Test large file upload handling"""
        # Create a larger test file (1MB)
        large_content = "data," * 250000  # Approximately 1MB
        
        response = await async_client.post("/api/v1/data/upload",
            headers=auth_headers,
            files={"file": ("large_test.csv", large_content, "text/csv")}
        )
        # Should handle large files appropriately
        assert response.status_code in [200, 413]  # Success or too large


class TestIntegration:
    """Integration tests"""
    
    @pytest.mark.asyncio
    async def test_full_ml_workflow(self, async_client: AsyncClient, auth_headers):
        """Test complete ML workflow"""
        # 1. Upload data
        test_data = "feature1,feature2,target\n1,2,0\n3,4,1\n5,6,0"
        upload_response = await async_client.post("/api/v1/data/upload",
            headers=auth_headers,
            files={"file": ("ml_data.csv", test_data, "text/csv")}
        )
        assert upload_response.status_code == 200
        
        # 2. Start training
        training_response = await async_client.post("/api/v1/ml/automl/train",
            headers=auth_headers,
            json={
                "name": "Integration Test Model",
                "dataset_id": "test-dataset",
                "target_column": "target",
                "model_type": "classification"
            }
        )
        assert training_response.status_code == 200
        
        # 3. Check model status
        model_id = training_response.json()["model_id"]
        status_response = await async_client.get(f"/api/v1/ml/models/{model_id}/training-status",
                                               headers=auth_headers)
        assert status_response.status_code == 200


# Fixtures
@pytest.fixture
async def async_client():
    """Create async test client"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
async def auth_headers(async_client: AsyncClient):
    """Create authentication headers"""
    # Register and login a test user
    await async_client.post("/api/v1/auth/register", json={
        "username": "testuser_auth",
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "full_name": "Auth Test User"
    })
    
    login_response = await async_client.post("/api/v1/auth/login", json={
        "username": "testuser_auth",
        "password": "TestPassword123!"
    })
    
    token = login_response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
