-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS data_management;
CREATE SCHEMA IF NOT EXISTS ml_models;
CREATE SCHEMA IF NOT EXISTS workflows;
CREATE SCHEMA IF NOT EXISTS monitoring;

-- Set default search path
ALTER DATABASE ai_platform SET search_path TO public, auth, data_management, ml_models, workflows, monitoring;

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'data_scientist', 'data_engineer', 'analyst', 'viewer');
CREATE TYPE pipeline_status AS ENUM ('draft', 'active', 'paused', 'completed', 'failed');
CREATE TYPE model_status AS ENUM ('training', 'trained', 'deployed', 'archived', 'failed');
CREATE TYPE data_source_type AS ENUM ('database', 'file', 'api', 'stream', 'cloud_storage');

-- Grant permissions
GRANT USAGE ON SCHEMA auth TO postgres;
GRANT USAGE ON SCHEMA data_management TO postgres;
GRANT USAGE ON SCHEMA ml_models TO postgres;
GRANT USAGE ON SCHEMA workflows TO postgres;
GRANT USAGE ON SCHEMA monitoring TO postgres;
