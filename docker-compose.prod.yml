version: '3.8'

services:
  # Reverse Proxy & Load Balancer
  nginx:
    image: nginx:alpine
    container_name: ai-platform-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infra/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./infra/nginx/ssl:/etc/nginx/ssl:ro
      - ./data/static:/var/www/static:ro
    depends_on:
      - frontend
      - backend
    networks:
      - ai-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Production Build
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: ai-platform-frontend-prod
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL:-https://api.yourdomain.com}
      - NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME:-AI Data Platform}
      - NEXT_PUBLIC_ENVIRONMENT=production
    networks:
      - ai-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Production
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: ai-platform-backend-prod
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - REFRESH_TOKEN_EXPIRE_DAYS=${REFRESH_TOKEN_EXPIRE_DAYS:-7}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - HUGGINGFACE_CACHE_DIR=/app/data/cache/huggingface
      - UPLOAD_DIR=/app/data/uploads
      - MODEL_STORAGE_PATH=/app/data/models
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - ai-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Workers
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: ai-platform-celery-worker-prod
    command: celery -A app.workers.celery_app worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - HUGGINGFACE_CACHE_DIR=/app/data/cache/huggingface
      - UPLOAD_DIR=/app/data/uploads
      - MODEL_STORAGE_PATH=/app/data/models
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - ai-platform-network
    restart: unless-stopped
    deploy:
      replicas: 2
    healthcheck:
      test: ["CMD", "celery", "-A", "app.workers.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: ai-platform-celery-beat-prod
    command: celery -A app.workers.celery_app beat --loglevel=info
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - ai-platform-network
    restart: unless-stopped

  # PostgreSQL Production
  postgres:
    image: postgres:15-alpine
    container_name: ai-platform-postgres-prod
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-ai_platform}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./infra/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./infra/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./logs/postgres:/var/log/postgresql
    ports:
      - "5432:5432"
    networks:
      - ai-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ai_platform}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Production
  redis:
    image: redis:7-alpine
    container_name: ai-platform-redis-prod
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data_prod:/data
      - ./infra/redis/redis.conf:/etc/redis/redis.conf:ro
      - ./logs/redis:/var/log/redis
    ports:
      - "6379:6379"
    networks:
      - ai-platform-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-platform-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./infra/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ai-platform-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: ai-platform-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infra/grafana/provisioning:/etc/grafana/provisioning:ro
    ports:
      - "3001:3000"
    networks:
      - ai-platform-network
    restart: unless-stopped

  # Log Management
  loki:
    image: grafana/loki:latest
    container_name: ai-platform-loki
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./infra/loki/loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    ports:
      - "3100:3100"
    networks:
      - ai-platform-network
    restart: unless-stopped

  promtail:
    image: grafana/promtail:latest
    container_name: ai-platform-promtail
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./infra/promtail/promtail-config.yml:/etc/promtail/config.yml:ro
      - ./logs:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    networks:
      - ai-platform-network
    restart: unless-stopped

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local

networks:
  ai-platform-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
