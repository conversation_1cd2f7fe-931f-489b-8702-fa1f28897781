version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ai-platform-postgres
    environment:
      POSTGRES_DB: ai_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - ai-platform-network

  # Redis for Celery and Caching
  redis:
    image: redis:7-alpine
    container_name: ai-platform-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-platform-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-platform-backend
    environment:
      - DATABASE_URL=********************************************/ai_platform
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./shared:/app/shared
    depends_on:
      - postgres
      - redis
    networks:
      - ai-platform-network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-platform-celery-worker
    environment:
      - DATABASE_URL=********************************************/ai_platform
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./shared:/app/shared
      - ./ml:/app/ml
    depends_on:
      - postgres
      - redis
    networks:
      - ai-platform-network
    command: celery -A app.workers.celery_app worker --loglevel=info

  # Celery Beat (Scheduler)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-platform-celery-beat
    environment:
      - DATABASE_URL=********************************************/ai_platform
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./shared:/app/shared
    depends_on:
      - postgres
      - redis
    networks:
      - ai-platform-network
    command: celery -A app.workers.celery_app beat --loglevel=info

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-platform-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - ai-platform-network

  # Jupyter Lab for ML Development
  jupyter:
    build:
      context: ./ml
      dockerfile: Dockerfile
    container_name: ai-platform-jupyter
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=ai-platform-token
    ports:
      - "8888:8888"
    volumes:
      - ./ml:/home/<USER>/work
      - ./data:/home/<USER>/work/data
      - ./shared:/home/<USER>/work/shared
    networks:
      - ai-platform-network

volumes:
  postgres_data:
  redis_data:

networks:
  ai-platform-network:
    driver: bridge
