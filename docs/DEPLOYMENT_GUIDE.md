# 🚀 AI Data Platform - Production Deployment Guide

This guide covers the complete production deployment process for the AI Data Platform.

## 📋 Prerequisites

### System Requirements
- **CPU**: 4+ cores (8+ recommended)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB+ SSD storage
- **Network**: Stable internet connection with static IP
- **OS**: Ubuntu 20.04+ LTS or CentOS 8+

### Software Requirements
- Docker 24.0+
- Docker Compose 2.0+
- Git 2.30+
- SSL certificates (Let's Encrypt recommended)
- Domain name with DNS access

### Cloud Provider Setup (Optional)
- **AWS**: ECS, RDS, ElastiCache, S3, Route 53
- **GCP**: Cloud Run, Cloud SQL, Memorystore, Cloud Storage
- **Azure**: Container Instances, Azure Database, Redis Cache

## 🔧 Pre-Deployment Setup

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
sudo mkdir -p /opt/ai-platform
sudo chown $USER:$USER /opt/ai-platform
cd /opt/ai-platform
```

### 2. Clone Repository

```bash
git clone https://github.com/Jainam1673/ai-data-platform.git .
git checkout main
```

### 3. SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot

# Generate SSL certificates
sudo certbot certonly --standalone -d yourdomain.com -d api.yourdomain.com

# Copy certificates to nginx directory
sudo mkdir -p infra/nginx/ssl
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem infra/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem infra/nginx/ssl/key.pem
sudo chown -R $USER:$USER infra/nginx/ssl
```

## ⚙️ Configuration

### 1. Environment Configuration

```bash
# Copy production environment template
cp .env.production .env.prod

# Edit configuration
nano .env.prod
```

**Critical settings to update:**
```bash
# Database
POSTGRES_PASSWORD=your_strong_database_password
DATABASE_URL=*************************************************************************/ai_platform_prod

# Redis
REDIS_PASSWORD=your_strong_redis_password
REDIS_URL=redis://:your_strong_redis_password@redis:6379/0

# Security
SECRET_KEY=your_generated_secret_key_here  # Generate with: openssl rand -hex 32

# Domain
DOMAIN=yourdomain.com
API_DOMAIN=api.yourdomain.com
NEXT_PUBLIC_API_URL=https://api.yourdomain.com

# Monitoring
GRAFANA_PASSWORD=your_grafana_admin_password
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# Email (Optional)
SMTP_HOST=smtp.yourdomain.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# Backup
S3_BACKUP_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

### 2. Nginx Configuration

Update `infra/nginx/nginx.conf` with your domain:

```nginx
server_name yourdomain.com www.yourdomain.com;
```

### 3. Database Initialization

```bash
# Create data directories
mkdir -p data/{uploads,models,processed,cache,backups} logs

# Set permissions
chmod 755 data logs
```

## 🚀 Deployment Process

### 1. Automated Deployment

```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Deploy to production
./scripts/deploy.sh production v1.0.0
```

### 2. Manual Deployment

```bash
# Build production images
docker build -t ai-platform-backend:latest -f backend/Dockerfile.prod backend/
docker build -t ai-platform-frontend:latest -f frontend/Dockerfile.prod frontend/

# Start services
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# Verify deployment
./scripts/test-platform.sh
```

## 🔍 Post-Deployment Verification

### 1. Health Checks

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Test endpoints
curl -f https://yourdomain.com/api/health
curl -f https://api.yourdomain.com/health
curl -f https://api.yourdomain.com/monitoring/health
```

### 2. Performance Testing

```bash
# Install k6 (if not already installed)
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
sudo apt-get update
sudo apt-get install k6

# Run load tests
k6 run tests/performance/load-test.js
```

### 3. Security Verification

```bash
# SSL certificate check
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Security headers check
curl -I https://yourdomain.com

# Port scan (should only show 80, 443)
nmap yourdomain.com
```

## 📊 Monitoring Setup

### 1. Access Monitoring Dashboards

- **Grafana**: https://yourdomain.com:3001 (admin/your_grafana_password)
- **Prometheus**: https://yourdomain.com:9090
- **Application Logs**: `docker-compose logs -f`

### 2. Set Up Alerts

Configure alerts in Grafana for:
- High CPU/Memory usage (>80%)
- High error rates (>5%)
- Slow response times (>2s)
- Service downtime

### 3. Log Management

```bash
# Set up log rotation
sudo nano /etc/logrotate.d/ai-platform

# Add configuration:
/opt/ai-platform/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
```

## 🔄 Backup and Recovery

### 1. Automated Backups

```bash
# Create backup script
cat > scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/ai-platform/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Database backup
docker-compose exec -T postgres pg_dump -U postgres ai_platform_prod > "$BACKUP_DIR/db_$DATE.sql"

# File backup
tar -czf "$BACKUP_DIR/files_$DATE.tar.gz" data/

# Upload to S3 (if configured)
if [ -n "$S3_BACKUP_BUCKET" ]; then
    aws s3 cp "$BACKUP_DIR/db_$DATE.sql" "s3://$S3_BACKUP_BUCKET/backups/"
    aws s3 cp "$BACKUP_DIR/files_$DATE.tar.gz" "s3://$S3_BACKUP_BUCKET/backups/"
fi

# Clean old backups (keep 30 days)
find "$BACKUP_DIR" -name "*.sql" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x scripts/backup.sh

# Add to crontab
echo "0 2 * * * /opt/ai-platform/scripts/backup.sh" | crontab -
```

### 2. Recovery Process

```bash
# Stop services
docker-compose -f docker-compose.prod.yml down

# Restore database
docker-compose -f docker-compose.prod.yml up -d postgres
cat backups/db_YYYYMMDD_HHMMSS.sql | docker-compose exec -T postgres psql -U postgres ai_platform_prod

# Restore files
tar -xzf backups/files_YYYYMMDD_HHMMSS.tar.gz

# Start all services
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 Maintenance

### 1. Regular Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker images
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Clean up old images
docker image prune -f
```

### 2. SSL Certificate Renewal

```bash
# Renew certificates (automated with cron)
sudo certbot renew --quiet

# Update nginx certificates
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem infra/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem infra/nginx/ssl/key.pem

# Reload nginx
docker-compose -f docker-compose.prod.yml exec nginx nginx -s reload
```

### 3. Database Maintenance

```bash
# Vacuum and analyze database
docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres ai_platform_prod -c "VACUUM ANALYZE;"

# Check database size
docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres ai_platform_prod -c "SELECT pg_size_pretty(pg_database_size('ai_platform_prod'));"
```

## 🚨 Troubleshooting

### Common Issues

1. **Service won't start**: Check logs with `docker-compose logs service-name`
2. **Database connection failed**: Verify credentials and network connectivity
3. **SSL certificate issues**: Check certificate validity and nginx configuration
4. **High memory usage**: Monitor with `docker stats` and adjust resource limits
5. **Slow performance**: Check database queries and enable caching

### Emergency Procedures

1. **Service outage**: Run health checks and restart affected services
2. **Data corruption**: Restore from latest backup
3. **Security breach**: Immediately change all passwords and review logs
4. **High load**: Scale horizontally or increase resources

## 📞 Support

For production support:
- **Documentation**: [docs/](../docs/)
- **Monitoring**: Grafana dashboards
- **Logs**: Centralized logging with Loki
- **Alerts**: Configured in Prometheus/Grafana

---

**Next Steps**: See [OPERATIONAL_RUNBOOK.md](OPERATIONAL_RUNBOOK.md) for day-to-day operations.
