'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Brain, 
  Play, 
  Pause, 
  Stop, 
  Download, 
  Upload,
  Settings,
  TrendingUp,
  Clock,
  Cpu,
  Database,
  RefreshCw,
  Plus,
  Search,
  Filter
} from 'lucide-react';
import Link from 'next/link';

// ML Model service
const modelService = {
  async fetchModels(filters = {}) {
    try {
      const params = new URLSearchParams(filters);
      const response = await fetch(`/api/v1/ml/models?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch models');
      return await response.json();
    } catch (error) {
      console.error('Error fetching models:', error);
      // Return mock data for demo
      return {
        items: [
          {
            id: '1',
            name: 'Customer Churn Prediction',
            algorithm: 'Random Forest',
            accuracy: 94.2,
            status: 'deployed',
            version: '1.2.0',
            created_at: '2024-01-10T10:30:00Z',
            training_time: 45,
            model_size: '12.5MB',
            framework: 'scikit-learn'
          },
          {
            id: '2',
            name: 'Fraud Detection Model',
            algorithm: 'XGBoost',
            accuracy: 96.8,
            status: 'training',
            version: '2.1.0',
            created_at: '2024-01-15T14:20:00Z',
            training_time: 120,
            model_size: '8.3MB',
            framework: 'xgboost'
          },
          {
            id: '3',
            name: 'Recommendation Engine',
            algorithm: 'Neural Network',
            accuracy: 89.5,
            status: 'deployed',
            version: '1.0.0',
            created_at: '2024-01-12T09:15:00Z',
            training_time: 180,
            model_size: '45.2MB',
            framework: 'tensorflow'
          },
          {
            id: '4',
            name: 'Price Optimization',
            algorithm: 'Linear Regression',
            accuracy: 92.1,
            status: 'failed',
            version: '1.1.0',
            created_at: '2024-01-14T16:45:00Z',
            training_time: 30,
            model_size: '2.1MB',
            framework: 'scikit-learn'
          }
        ],
        total: 4
      };
    }
  },

  async getModelMetrics(modelId) {
    try {
      const response = await fetch(`/api/v1/ml/models/${modelId}/metrics`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch model metrics');
      return await response.json();
    } catch (error) {
      console.error('Error fetching model metrics:', error);
      return {
        accuracy: 94.2,
        precision: 93.8,
        recall: 94.6,
        f1_score: 94.2,
        predictions_today: 1250,
        avg_inference_time: 15
      };
    }
  },

  async deployModel(modelId) {
    try {
      const response = await fetch(`/api/v1/ml/models/${modelId}/deploy`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error('Failed to deploy model');
      return await response.json();
    } catch (error) {
      console.error('Error deploying model:', error);
      throw error;
    }
  },

  async stopModel(modelId) {
    try {
      const response = await fetch(`/api/v1/ml/models/${modelId}/stop`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to stop model');
      return await response.json();
    } catch (error) {
      console.error('Error stopping model:', error);
      throw error;
    }
  }
};

export default function ModelsPage() {
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [frameworkFilter, setFrameworkFilter] = useState('all');

  useEffect(() => {
    loadModels();
  }, [searchTerm, statusFilter, frameworkFilter]);

  const loadModels = async () => {
    try {
      setError(null);
      const filters = {};
      if (searchTerm) filters.search = searchTerm;
      if (statusFilter !== 'all') filters.status = statusFilter;
      if (frameworkFilter !== 'all') filters.framework = frameworkFilter;

      const data = await modelService.fetchModels(filters);
      setModels(data.items || []);
    } catch (err) {
      setError('Failed to load models');
      console.error('Models load error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeploy = async (modelId) => {
    try {
      await modelService.deployModel(modelId);
      await loadModels(); // Refresh the list
    } catch (error) {
      setError('Failed to deploy model');
    }
  };

  const handleStop = async (modelId) => {
    try {
      await modelService.stopModel(modelId);
      await loadModels(); // Refresh the list
    } catch (error) {
      setError('Failed to stop model');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'deployed': return 'default';
      case 'training': return 'secondary';
      case 'failed': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'deployed': return <Play className="h-4 w-4" />;
      case 'training': return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'failed': return <Stop className="h-4 w-4" />;
      default: return <Pause className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading models...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">ML Models</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadModels}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Link href="/models/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Train New Model
            </Button>
          </Link>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search models..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="deployed">Deployed</SelectItem>
                <SelectItem value="training">Training</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={frameworkFilter} onValueChange={setFrameworkFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by framework" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Frameworks</SelectItem>
                <SelectItem value="scikit-learn">Scikit-learn</SelectItem>
                <SelectItem value="tensorflow">TensorFlow</SelectItem>
                <SelectItem value="pytorch">PyTorch</SelectItem>
                <SelectItem value="xgboost">XGBoost</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Models Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {models.map((model) => (
          <Card key={model.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{model.name}</CardTitle>
                <Badge variant={getStatusColor(model.status)} className="flex items-center gap-1">
                  {getStatusIcon(model.status)}
                  {model.status}
                </Badge>
              </div>
              <CardDescription>
                {model.algorithm} • v{model.version}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Metrics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Accuracy</p>
                  <p className="font-bold">{model.accuracy}%</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Framework</p>
                  <p className="font-bold">{model.framework}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Training Time</p>
                  <p className="font-bold">{model.training_time}min</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Model Size</p>
                  <p className="font-bold">{model.model_size}</p>
                </div>
              </div>

              {/* Progress for training models */}
              {model.status === 'training' && (
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Training Progress</span>
                    <span>75%</span>
                  </div>
                  <Progress value={75} />
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                {model.status === 'deployed' ? (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleStop(model.id)}
                  >
                    <Stop className="h-4 w-4 mr-1" />
                    Stop
                  </Button>
                ) : model.status === 'training' ? (
                  <Button variant="outline" size="sm" disabled>
                    <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                    Training
                  </Button>
                ) : (
                  <Button 
                    size="sm" 
                    onClick={() => handleDeploy(model.id)}
                    disabled={model.status === 'failed'}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Deploy
                  </Button>
                )}
                
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-1" />
                  Config
                </Button>
                
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>

              {/* Created date */}
              <p className="text-xs text-muted-foreground">
                Created {new Date(model.created_at).toLocaleDateString()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {models.length === 0 && !loading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Brain className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No models found</h3>
            <p className="text-muted-foreground text-center mb-4">
              Get started by training your first machine learning model.
            </p>
            <Link href="/models/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Train New Model
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Models</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{models.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Deployed</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {models.filter(m => m.status === 'deployed').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Training</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {models.filter(m => m.status === 'training').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Accuracy</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {models.length > 0 
                ? (models.reduce((sum, m) => sum + m.accuracy, 0) / models.length).toFixed(1)
                : 0
              }%
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
