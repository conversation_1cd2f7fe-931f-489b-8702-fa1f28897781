'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/contexts/auth-context';
import { ProtectedRoute } from '@/components/protected-route';
import { Navigation } from '@/components/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FolderOpen, 
  Database, 
  Workflow, 
  Brain, 
  Plus, 
  TrendingUp, 
  Activity,
  Users,
  Clock
} from 'lucide-react';
import Link from 'next/link';
import { projectService } from '@/lib/services/projects';
import { dataService } from '@/lib/services/data';
import { workflowService } from '@/lib/services/workflows';
import { mlService } from '@/lib/services/ml';

interface DashboardStats {
  projects: number;
  dataSources: number;
  workflows: number;
  models: number;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    projects: 0,
    dataSources: 0,
    workflows: 0,
    models: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [projects, dataSources, workflows, models] = await Promise.all([
          projectService.getProjects({ size: 1 }),
          dataService.getDataSources({ size: 1 }),
          workflowService.getWorkflows({ size: 1 }),
          mlService.getModels({ size: 1 })
        ]);

        setStats({
          projects: projects.total,
          dataSources: dataSources.total,
          workflows: workflows.total,
          models: models.total
        });
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const quickActions = [
    {
      title: 'Create Project',
      description: 'Start a new data science project',
      icon: FolderOpen,
      href: '/projects/new',
      color: 'bg-blue-500'
    },
    {
      title: 'Add Data Source',
      description: 'Connect to your data',
      icon: Database,
      href: '/data/new',
      color: 'bg-green-500'
    },
    {
      title: 'Build Workflow',
      description: 'Create a data pipeline',
      icon: Workflow,
      href: '/workflows/new',
      color: 'bg-purple-500'
    },
    {
      title: 'Train Model',
      description: 'Build an AI model',
      icon: Brain,
      href: '/models/new',
      color: 'bg-orange-500'
    }
  ];

  const statCards = [
    {
      title: 'Projects',
      value: stats.projects,
      icon: FolderOpen,
      description: 'Active projects',
      color: 'text-blue-600'
    },
    {
      title: 'Data Sources',
      value: stats.dataSources,
      icon: Database,
      description: 'Connected sources',
      color: 'text-green-600'
    },
    {
      title: 'Workflows',
      value: stats.workflows,
      icon: Workflow,
      description: 'Built workflows',
      color: 'text-purple-600'
    },
    {
      title: 'ML Models',
      value: stats.models,
      icon: Brain,
      description: 'Trained models',
      color: 'text-orange-600'
    }
  ];

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Welcome back, {user?.full_name || user?.username}!
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Here's what's happening with your AI data platform today.
              </p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {statCards.map((stat) => (
                <Card key={stat.title}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {stat.title}
                    </CardTitle>
                    <stat.icon className={`h-4 w-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {loading ? '...' : stat.value}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {stat.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {quickActions.map((action) => (
                  <Card key={action.title} className="hover:shadow-md transition-shadow cursor-pointer">
                    <Link href={action.href}>
                      <CardHeader className="pb-3">
                        <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-3`}>
                          <action.icon className="h-5 w-5 text-white" />
                        </div>
                        <CardTitle className="text-base">{action.title}</CardTitle>
                        <CardDescription className="text-sm">
                          {action.description}
                        </CardDescription>
                      </CardHeader>
                    </Link>
                  </Card>
                ))}
              </div>
            </div>

            {/* Recent Activity & Getting Started */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Activity className="mr-2 h-5 w-5" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Welcome to AI Data Platform!</p>
                        <p className="text-xs text-muted-foreground">Get started by creating your first project</p>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        <Clock className="mr-1 h-3 w-3" />
                        Now
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Getting Started */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="mr-2 h-5 w-5" />
                    Getting Started
                  </CardTitle>
                  <CardDescription>
                    Complete these steps to get the most out of the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Create your first project</span>
                      <Badge variant={stats.projects > 0 ? "default" : "secondary"}>
                        {stats.projects > 0 ? "Done" : "Pending"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Connect a data source</span>
                      <Badge variant={stats.dataSources > 0 ? "default" : "secondary"}>
                        {stats.dataSources > 0 ? "Done" : "Pending"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Build your first workflow</span>
                      <Badge variant={stats.workflows > 0 ? "default" : "secondary"}>
                        {stats.workflows > 0 ? "Done" : "Pending"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Train an ML model</span>
                      <Badge variant={stats.models > 0 ? "default" : "secondary"}>
                        {stats.models > 0 ? "Done" : "Pending"}
                      </Badge>
                    </div>
                    
                    <div className="pt-2">
                      <div className="flex items-center justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{Math.round(((stats.projects > 0 ? 1 : 0) + (stats.dataSources > 0 ? 1 : 0) + (stats.workflows > 0 ? 1 : 0) + (stats.models > 0 ? 1 : 0)) / 4 * 100)}%</span>
                      </div>
                      <Progress 
                        value={((stats.projects > 0 ? 1 : 0) + (stats.dataSources > 0 ? 1 : 0) + (stats.workflows > 0 ? 1 : 0) + (stats.models > 0 ? 1 : 0)) / 4 * 100} 
                        className="h-2"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
