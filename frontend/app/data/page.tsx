'use client';

import { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/protected-route';
import { Navigation } from '@/components/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Database, 
  Calendar,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import Link from 'next/link';
import { dataService } from '@/lib/services/data';
import { DataSource } from '@/lib/api';
import { toast } from 'sonner';

export default function DataSourcesPage() {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchDataSources = async () => {
      try {
        const response = await dataService.getDataSources({ size: 50 });
        setDataSources(response.items);
      } catch (error) {
        console.error('Failed to fetch data sources:', error);
        toast.error('Failed to load data sources');
      } finally {
        setLoading(false);
      }
    };

    fetchDataSources();
  }, []);

  const filteredDataSources = dataSources.filter(dataSource =>
    dataSource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dataSource.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'testing': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'testing': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'database': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'file': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'api': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'stream': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Data Sources
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  Connect and manage your data sources
                </p>
              </div>
              <Button asChild>
                <Link href="/data/new">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Data Source
                </Link>
              </Button>
            </div>

            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search data sources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Data Sources Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredDataSources.length === 0 ? (
              <div className="text-center py-12">
                <Database className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  {searchTerm ? 'No data sources found' : 'No data sources yet'}
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {searchTerm 
                    ? 'Try adjusting your search terms' 
                    : 'Get started by connecting your first data source'
                  }
                </p>
                {!searchTerm && (
                  <div className="mt-6">
                    <Button asChild>
                      <Link href="/data/new">
                        <Plus className="mr-2 h-4 w-4" />
                        Add Data Source
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredDataSources.map((dataSource) => (
                  <Card key={dataSource.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center">
                            <Link 
                              href={`/data/${dataSource.id}`}
                              className="hover:text-primary transition-colors"
                            >
                              {dataSource.name}
                            </Link>
                            <div className="ml-2">
                              {getStatusIcon(dataSource.status)}
                            </div>
                          </CardTitle>
                          <CardDescription className="mt-1">
                            {dataSource.description || 'No description'}
                          </CardDescription>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mt-3">
                        <Badge className={getStatusColor(dataSource.status)}>
                          {dataSource.status}
                        </Badge>
                        <Badge className={getTypeColor(dataSource.type)}>
                          {dataSource.type}
                        </Badge>
                        {dataSource.is_public && (
                          <Badge variant="outline">
                            Public
                          </Badge>
                        )}
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="text-gray-500">Type: </span>
                          <span className="font-medium">{dataSource.type.toUpperCase()}</span>
                        </div>
                        
                        {dataSource.last_sync && (
                          <div className="text-sm">
                            <span className="text-gray-500">Last sync: </span>
                            <span>{new Date(dataSource.last_sync).toLocaleDateString()}</span>
                          </div>
                        )}
                        
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <Calendar className="mr-1 h-3 w-3" />
                            Created {new Date(dataSource.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
