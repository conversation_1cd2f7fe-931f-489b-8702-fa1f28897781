'use client';

import { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/protected-route';
import { Navigation } from '@/components/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Search, 
  Brain, 
  Calendar,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { mlService } from '@/lib/services/ml';
import { MLModel } from '@/lib/api';
import { toast } from 'sonner';

export default function ModelsPage() {
  const [models, setModels] = useState<MLModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await mlService.getModels({ size: 50 });
        setModels(response.items);
      } catch (error) {
        console.error('Failed to fetch models:', error);
        toast.error('Failed to load models');
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, []);

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    model.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'trained': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'deployed': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'training': return <Clock className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trained': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'deployed': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'training': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'classification': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'regression': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'clustering': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'deep_learning': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getFrameworkColor = (framework: string) => {
    switch (framework) {
      case 'scikit_learn': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'pytorch': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'tensorflow': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'autogluon': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  ML Models
                </h1>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  Train and deploy machine learning models
                </p>
              </div>
              <Button asChild>
                <Link href="/models/new">
                  <Plus className="mr-2 h-4 w-4" />
                  New Model
                </Link>
              </Button>
            </div>

            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search models..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Models Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredModels.length === 0 ? (
              <div className="text-center py-12">
                <Brain className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  {searchTerm ? 'No models found' : 'No models yet'}
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {searchTerm 
                    ? 'Try adjusting your search terms' 
                    : 'Get started by training your first ML model'
                  }
                </p>
                {!searchTerm && (
                  <div className="mt-6">
                    <Button asChild>
                      <Link href="/models/new">
                        <Plus className="mr-2 h-4 w-4" />
                        New Model
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredModels.map((model) => (
                  <Card key={model.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg flex items-center">
                            <Link 
                              href={`/models/${model.id}`}
                              className="hover:text-primary transition-colors"
                            >
                              {model.name}
                            </Link>
                            <div className="ml-2">
                              {getStatusIcon(model.status)}
                            </div>
                          </CardTitle>
                          <CardDescription className="mt-1">
                            {model.description || 'No description'}
                          </CardDescription>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mt-3">
                        <Badge className={getStatusColor(model.status)}>
                          {model.status}
                        </Badge>
                        <Badge className={getTypeColor(model.model_type)}>
                          {model.model_type.replace('_', ' ')}
                        </Badge>
                        <Badge className={getFrameworkColor(model.framework)}>
                          {model.framework.replace('_', ' ')}
                        </Badge>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="text-center">
                            <div className="font-medium">{model.version}</div>
                            <div className="text-gray-500 text-xs">Version</div>
                          </div>
                          <div className="text-center">
                            <div className="font-medium">{model.prediction_count}</div>
                            <div className="text-gray-500 text-xs">Predictions</div>
                          </div>
                        </div>
                        
                        {model.validation_score && (
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>Validation Score</span>
                              <span>{(model.validation_score * 100).toFixed(1)}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${model.validation_score * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        )}
                        
                        {model.model_size_mb && (
                          <div className="text-sm">
                            <span className="text-gray-500">Size: </span>
                            <span>{model.model_size_mb.toFixed(1)} MB</span>
                          </div>
                        )}
                        
                        <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <Calendar className="mr-1 h-3 w-3" />
                            Created {new Date(model.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
