'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ProtectedRoute } from '@/components/protected-route';
import { Navigation } from '@/components/navigation';
import { WorkflowCanvasProvider } from '@/components/workflow-builder/workflow-canvas';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Play, 
  Settings, 
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { workflowService } from '@/lib/services/workflows';

export default function WorkflowBuilderPage() {
  const router = useRouter();
  const [workflowName, setWorkflowName] = useState('Untitled Workflow');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [workflowType, setWorkflowType] = useState('data_pipeline');
  const [saving, setSaving] = useState(false);
  const [executing, setExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const handleSave = async (nodes: any[], edges: any[]) => {
    if (!workflowName.trim()) {
      setShowSaveDialog(true);
      return;
    }

    setSaving(true);
    try {
      const workflowData = {
        name: workflowName,
        description: workflowDescription,
        type: workflowType,
        definition: {
          version: '1.0',
          created_at: new Date().toISOString()
        },
        nodes,
        edges,
        config: {
          auto_save: true,
          version: '1.0'
        }
      };

      await workflowService.createWorkflow(workflowData);
      toast.success('Workflow saved successfully!');
      
    } catch (error: any) {
      console.error('Failed to save workflow:', error);
      toast.error(error.response?.data?.message || 'Failed to save workflow');
    } finally {
      setSaving(false);
    }
  };

  const handleExecute = async (nodes: any[], edges: any[]) => {
    if (nodes.length === 0) {
      toast.error('Add some nodes to execute the workflow');
      return;
    }

    setExecuting(true);
    setExecutionStatus('starting');
    
    try {
      // First save the workflow if it has a name
      if (workflowName.trim()) {
        await handleSave(nodes, edges);
      }

      // Simulate workflow execution
      setExecutionStatus('running');
      
      // In a real implementation, this would call the actual execution API
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setExecutionStatus('completed');
      toast.success('Workflow executed successfully!');
      
      // Reset status after a delay
      setTimeout(() => setExecutionStatus(null), 5000);
      
    } catch (error: any) {
      console.error('Failed to execute workflow:', error);
      setExecutionStatus('failed');
      toast.error(error.response?.data?.message || 'Failed to execute workflow');
      
      // Reset status after a delay
      setTimeout(() => setExecutionStatus(null), 5000);
    } finally {
      setExecuting(false);
    }
  };

  const handleSaveWithDetails = async () => {
    if (!workflowName.trim()) {
      toast.error('Please enter a workflow name');
      return;
    }
    
    setShowSaveDialog(false);
    // The actual save will be handled by the canvas component
    toast.success('Workflow details updated');
  };

  const getExecutionStatusBadge = () => {
    switch (executionStatus) {
      case 'starting':
        return (
          <Badge variant="secondary" className="animate-pulse">
            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
            Starting
          </Badge>
        );
      case 'running':
        return (
          <Badge variant="secondary" className="animate-pulse">
            <Clock className="mr-1 h-3 w-3" />
            Running
          </Badge>
        );
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <ProtectedRoute>
      <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
        <Navigation />
        
        {/* Header */}
        <div className="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-6">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            
            <div className="flex items-center space-x-3">
              <div>
                <h1 className="text-lg font-semibold">{workflowName}</h1>
                <p className="text-sm text-muted-foreground">
                  {workflowDescription || 'No description'}
                </p>
              </div>
              
              {getExecutionStatusBadge()}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Workflow Settings</DialogTitle>
                  <DialogDescription>
                    Configure your workflow details and settings.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={workflowName}
                      onChange={(e) => setWorkflowName(e.target.value)}
                      placeholder="Enter workflow name"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={workflowDescription}
                      onChange={(e) => setWorkflowDescription(e.target.value)}
                      placeholder="Describe what this workflow does"
                      rows={3}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <Select value={workflowType} onValueChange={setWorkflowType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select workflow type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="data_pipeline">Data Pipeline</SelectItem>
                        <SelectItem value="ml_pipeline">ML Pipeline</SelectItem>
                        <SelectItem value="analysis_workflow">Analysis Workflow</SelectItem>
                        <SelectItem value="deployment_workflow">Deployment Workflow</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSaveWithDetails}>
                    Save Settings
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        {/* Canvas */}
        <div className="flex-1">
          <WorkflowCanvasProvider
            onSave={handleSave}
            onExecute={handleExecute}
          />
        </div>
        
        {/* Status Bar */}
        <div className="h-8 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between px-6 text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Workflow Builder v1.0</span>
            <span>•</span>
            <span>Drag components from the palette to build your workflow</span>
          </div>
          
          <div className="flex items-center space-x-4">
            {saving && (
              <span className="flex items-center">
                <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                Saving...
              </span>
            )}
            {executing && (
              <span className="flex items-center">
                <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                Executing...
              </span>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
