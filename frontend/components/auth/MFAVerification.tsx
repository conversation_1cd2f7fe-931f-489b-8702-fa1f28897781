'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Smartphone, 
  AlertTriangle,
  RefreshCw,
  Key
} from 'lucide-react';

interface MFAVerificationProps {
  onSuccess: (token: string) => void;
  onCancel?: () => void;
  loading?: boolean;
}

export default function MFAVerification({ onSuccess, onCancel, loading = false }: MFAVerificationProps) {
  const [verificationCode, setVerificationCode] = useState('');
  const [useBackupCode, setUseBackupCode] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!verificationCode) {
      setError('Please enter a verification code');
      return;
    }

    if (!useBackupCode && verificationCode.length !== 6) {
      setError('Please enter a 6-digit verification code');
      return;
    }

    if (useBackupCode && verificationCode.length < 8) {
      setError('Please enter a valid backup code');
      return;
    }

    setError('');
    onSuccess(verificationCode);
  };

  const handleCodeChange = (value: string) => {
    if (useBackupCode) {
      // Backup codes are alphanumeric
      setVerificationCode(value.toUpperCase().slice(0, 10));
    } else {
      // TOTP codes are 6 digits
      setVerificationCode(value.replace(/\D/g, '').slice(0, 6));
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          {useBackupCode ? (
            <Key className="h-6 w-6 text-blue-600" />
          ) : (
            <Shield className="h-6 w-6 text-blue-600" />
          )}
        </div>
        <CardTitle>Two-Factor Authentication</CardTitle>
        <CardDescription>
          {useBackupCode 
            ? 'Enter one of your backup codes'
            : 'Enter the 6-digit code from your authenticator app'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="verification-code">
              {useBackupCode ? 'Backup Code' : 'Verification Code'}
            </Label>
            <Input
              id="verification-code"
              type="text"
              placeholder={useBackupCode ? 'ABCD-EFGH' : '000000'}
              value={verificationCode}
              onChange={(e) => handleCodeChange(e.target.value)}
              className="text-center text-lg font-mono"
              maxLength={useBackupCode ? 10 : 6}
              autoComplete="one-time-code"
              autoFocus
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Button 
              type="submit" 
              className="w-full"
              disabled={loading || !verificationCode}
            >
              {loading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
              Verify
            </Button>

            <Button
              type="button"
              variant="ghost"
              className="w-full text-sm"
              onClick={() => {
                setUseBackupCode(!useBackupCode);
                setVerificationCode('');
                setError('');
              }}
            >
              {useBackupCode ? (
                <>
                  <Smartphone className="h-4 w-4 mr-2" />
                  Use authenticator app instead
                </>
              ) : (
                <>
                  <Key className="h-4 w-4 mr-2" />
                  Use backup code instead
                </>
              )}
            </Button>

            {onCancel && (
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={onCancel}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>

        {!useBackupCode && (
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <div className="flex items-start space-x-3">
              <Smartphone className="h-5 w-5 text-muted-foreground mt-0.5" />
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-1">Having trouble?</p>
                <ul className="space-y-1">
                  <li>• Make sure your device's time is correct</li>
                  <li>• Try refreshing your authenticator app</li>
                  <li>• Use a backup code if needed</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {useBackupCode && (
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Using Backup Code</p>
                <p>Each backup code can only be used once. Make sure to generate new backup codes after using several of them.</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
