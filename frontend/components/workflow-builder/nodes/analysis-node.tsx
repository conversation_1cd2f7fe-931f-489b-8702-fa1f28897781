'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BarChart3, Settings, TrendingUp } from 'lucide-react';

interface AnalysisNodeData {
  label: string;
  config: {
    type: string;
    metrics?: string[];
    status?: string;
  };
}

function AnalysisNode({ data, selected }: NodeProps<AnalysisNodeData>) {
  const getAnalysisTypeColor = (type: string) => {
    switch (type) {
      case 'summary': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'correlation': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'distribution': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'trend': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
              <BarChart3 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium">{data.label}</h4>
              <p className="text-xs text-muted-foreground">Analysis</p>
            </div>
          </div>
          <Settings className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <Badge className={getAnalysisTypeColor(data.config.type)}>
            {data.config.type}
          </Badge>
          
          {data.config.metrics && data.config.metrics.length > 0 && (
            <div className="text-xs text-muted-foreground">
              {data.config.metrics.length} metric(s)
            </div>
          )}
          
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <TrendingUp className="h-3 w-3" />
          </div>
        </div>
      </CardContent>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-orange-500 border-2 border-white"
      />
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-orange-500 border-2 border-white"
      />
    </Card>
  );
}

export default memo(AnalysisNode);
