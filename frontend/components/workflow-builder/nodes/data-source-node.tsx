'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Database, Settings } from 'lucide-react';

interface DataSourceNodeData {
  label: string;
  config: {
    type: string;
    source: string;
    status?: string;
  };
}

function DataSourceNode({ data, selected }: NodeProps<DataSourceNodeData>) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'connecting': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    // You can expand this with more specific icons
    return Database;
  };

  const TypeIcon = getTypeIcon(data.config.type);

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <TypeIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium">{data.label}</h4>
              <p className="text-xs text-muted-foreground">{data.config.type.toUpperCase()}</p>
            </div>
          </div>
          <Settings className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-xs">
            <span className="text-muted-foreground">Source: </span>
            <span className="font-mono">{data.config.source || 'Not configured'}</span>
          </div>
          
          {data.config.status && (
            <Badge className={getStatusColor(data.config.status)}>
              {data.config.status}
            </Badge>
          )}
        </div>
      </CardContent>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </Card>
  );
}

export default memo(DataSourceNode);
