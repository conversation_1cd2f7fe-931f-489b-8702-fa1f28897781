'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, Settings, Zap } from 'lucide-react';

interface MLNodeData {
  label: string;
  config: {
    algorithm: string;
    parameters?: Record<string, any>;
    status?: string;
  };
}

function MLNode({ data, selected }: NodeProps<MLNodeData>) {
  const getAlgorithmColor = (algorithm: string) => {
    switch (algorithm) {
      case 'linear_regression': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'random_forest': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'neural_network': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'svm': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'clustering': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'trained': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'training': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-pink-100 dark:bg-pink-900 rounded-lg flex items-center justify-center">
              <Brain className="h-4 w-4 text-pink-600 dark:text-pink-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium">{data.label}</h4>
              <p className="text-xs text-muted-foreground">ML Model</p>
            </div>
          </div>
          <Settings className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <Badge className={getAlgorithmColor(data.config.algorithm)}>
            {data.config.algorithm.replace('_', ' ')}
          </Badge>
          
          {data.config.status && (
            <Badge className={getStatusColor(data.config.status)}>
              {data.config.status}
            </Badge>
          )}
          
          {data.config.parameters && Object.keys(data.config.parameters).length > 0 && (
            <div className="text-xs text-muted-foreground">
              {Object.keys(data.config.parameters).length} parameter(s)
            </div>
          )}
          
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <Zap className="h-3 w-3" />
          </div>
        </div>
      </CardContent>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-pink-500 border-2 border-white"
      />
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-pink-500 border-2 border-white"
      />
    </Card>
  );
}

export default memo(MLNode);
