'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileOutput, Settings, Download } from 'lucide-react';

interface OutputNodeData {
  label: string;
  config: {
    format: string;
    destination: string;
    status?: string;
  };
}

function OutputNode({ data, selected }: NodeProps<OutputNodeData>) {
  const getFormatColor = (format: string) => {
    switch (format) {
      case 'csv': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'json': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'parquet': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'database': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'api': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'saved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'saving': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <FileOutput className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium">{data.label}</h4>
              <p className="text-xs text-muted-foreground">Output</p>
            </div>
          </div>
          <Settings className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <Badge className={getFormatColor(data.config.format)}>
            {data.config.format.toUpperCase()}
          </Badge>
          
          {data.config.status && (
            <Badge className={getStatusColor(data.config.status)}>
              {data.config.status}
            </Badge>
          )}
          
          <div className="text-xs">
            <span className="text-muted-foreground">To: </span>
            <span className="font-mono">{data.config.destination || 'Not configured'}</span>
          </div>
          
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <Download className="h-3 w-3" />
          </div>
        </div>
      </CardContent>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
    </Card>
  );
}

export default memo(OutputNode);
