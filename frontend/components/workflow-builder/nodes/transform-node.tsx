'use client';

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Filter, Settings, ArrowRight } from 'lucide-react';

interface TransformNodeData {
  label: string;
  config: {
    operation: string;
    conditions?: any[];
    status?: string;
  };
}

function TransformNode({ data, selected }: NodeProps<TransformNodeData>) {
  const getOperationColor = (operation: string) => {
    switch (operation) {
      case 'filter': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'aggregate': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'join': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'sort': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Filter className="h-4 w-4 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium">{data.label}</h4>
              <p className="text-xs text-muted-foreground">Transform</p>
            </div>
          </div>
          <Settings className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <Badge className={getOperationColor(data.config.operation)}>
            {data.config.operation}
          </Badge>
          
          {data.config.conditions && data.config.conditions.length > 0 && (
            <div className="text-xs text-muted-foreground">
              {data.config.conditions.length} condition(s)
            </div>
          )}
          
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <ArrowRight className="h-3 w-3" />
          </div>
        </div>
      </CardContent>

      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-purple-500 border-2 border-white"
      />
    </Card>
  );
}

export default memo(TransformNode);
