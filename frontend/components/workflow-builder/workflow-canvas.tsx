'use client';

import React, { useState, useCallback, useRef } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  ReactFlowInstance,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  Filter, 
  BarChart3, 
  Brain, 
  FileOutput,
  Play,
  Save,
  Download
} from 'lucide-react';

// Custom node types
import DataSourceNode from './nodes/data-source-node';
import TransformNode from './nodes/transform-node';
import AnalysisNode from './nodes/analysis-node';
import MLNode from './nodes/ml-node';
import OutputNode from './nodes/output-node';

const nodeTypes = {
  dataSource: DataSourceNode,
  transform: TransformNode,
  analysis: AnalysisNode,
  ml: MLNode,
  output: OutputNode,
};

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'dataSource',
    position: { x: 100, y: 100 },
    data: { 
      label: 'Data Source',
      config: {
        type: 'csv',
        source: 'sample.csv'
      }
    },
  },
];

const initialEdges: Edge[] = [];

interface WorkflowCanvasProps {
  workflowId?: string;
  onSave?: (nodes: Node[], edges: Edge[]) => void;
  onExecute?: (nodes: Node[], edges: Edge[]) => void;
  readOnly?: boolean;
}

export default function WorkflowCanvas({ 
  workflowId, 
  onSave, 
  onExecute, 
  readOnly = false 
}: WorkflowCanvasProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type || !reactFlowInstance || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: { 
          label: getNodeLabel(type),
          config: getDefaultConfig(type)
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  const getNodeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      dataSource: 'Data Source',
      transform: 'Transform',
      analysis: 'Analysis',
      ml: 'ML Model',
      output: 'Output'
    };
    return labels[type] || type;
  };

  const getDefaultConfig = (type: string): any => {
    const configs: Record<string, any> = {
      dataSource: { type: 'csv', source: '' },
      transform: { operation: 'filter', conditions: [] },
      analysis: { type: 'summary', metrics: [] },
      ml: { algorithm: 'linear_regression', parameters: {} },
      output: { format: 'csv', destination: '' }
    };
    return configs[type] || {};
  };

  const handleSave = () => {
    if (onSave) {
      onSave(nodes, edges);
    }
  };

  const handleExecute = () => {
    if (onExecute) {
      onExecute(nodes, edges);
    }
  };

  const handleExport = () => {
    const workflow = {
      nodes,
      edges,
      viewport: reactFlowInstance?.getViewport(),
    };
    
    const dataStr = JSON.stringify(workflow, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `workflow-${workflowId || 'new'}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <div className="h-full flex">
      {/* Node Palette */}
      {!readOnly && (
        <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4">
          <h3 className="font-semibold mb-4">Components</h3>
          <div className="space-y-2">
            <NodePaletteItem
              type="dataSource"
              icon={Database}
              label="Data Source"
              description="Connect to data"
            />
            <NodePaletteItem
              type="transform"
              icon={Filter}
              label="Transform"
              description="Process data"
            />
            <NodePaletteItem
              type="analysis"
              icon={BarChart3}
              label="Analysis"
              description="Analyze data"
            />
            <NodePaletteItem
              type="ml"
              icon={Brain}
              label="ML Model"
              description="Train/predict"
            />
            <NodePaletteItem
              type="output"
              icon={FileOutput}
              label="Output"
              description="Save results"
            />
          </div>
        </div>
      )}

      {/* Canvas */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
          <div className="flex items-center space-x-2">
            <Badge variant="outline">
              {nodes.length} nodes
            </Badge>
            <Badge variant="outline">
              {edges.length} connections
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            {!readOnly && (
              <>
                <Button variant="outline" size="sm" onClick={handleSave}>
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </Button>
                <Button variant="outline" size="sm" onClick={handleExport}>
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                <Button size="sm" onClick={handleExecute}>
                  <Play className="mr-2 h-4 w-4" />
                  Execute
                </Button>
              </>
            )}
          </div>
        </div>

        {/* React Flow Canvas */}
        <div className="flex-1" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap />
            <Background variant="dots" gap={12} size={1} />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
}

interface NodePaletteItemProps {
  type: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  description: string;
}

function NodePaletteItem({ type, icon: Icon, label, description }: NodePaletteItemProps) {
  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Card 
      className="cursor-grab active:cursor-grabbing hover:shadow-md transition-shadow"
      draggable
      onDragStart={(event) => onDragStart(event, type)}
    >
      <CardContent className="p-3">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon className="h-4 w-4 text-primary" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{label}</p>
            <p className="text-xs text-muted-foreground truncate">{description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Wrapper component with ReactFlowProvider
export function WorkflowCanvasProvider(props: WorkflowCanvasProps) {
  return (
    <ReactFlowProvider>
      <WorkflowCanvas {...props} />
    </ReactFlowProvider>
  );
}
