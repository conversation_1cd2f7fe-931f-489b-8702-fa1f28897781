# PostgreSQL Production Configuration
# Optimized for AI Data Platform workloads

# Connection Settings
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 256MB                  # 25% of RAM for dedicated server
effective_cache_size = 1GB              # 75% of RAM
work_mem = 4MB                          # Per-operation memory
maintenance_work_mem = 64MB             # Maintenance operations
max_wal_size = 2GB
min_wal_size = 80MB

# Checkpoint Settings
checkpoint_completion_target = 0.9
checkpoint_timeout = 10min
checkpoint_warning = 30s

# Query Planner
random_page_cost = 1.1                  # SSD optimized
effective_io_concurrency = 200          # SSD optimized
seq_page_cost = 1.0

# Write Ahead Logging
wal_level = replica
wal_compression = on
wal_buffers = 16MB
wal_writer_delay = 200ms

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Autovacuum Settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = 200

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000       # Log slow queries (1 second)
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# Statistics
track_activities = on
track_counts = on
track_io_timing = on
track_functions = pl
stats_temp_directory = '/var/run/postgresql/stats_temp'

# Lock Management
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

# Client Connection Defaults
default_text_search_config = 'pg_catalog.english'
timezone = 'UTC'
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Parallel Query Settings
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2

# JIT Settings (PostgreSQL 11+)
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000

# Security
ssl = off                               # Handled by nginx
password_encryption = scram-sha-256

# Replication (for future scaling)
max_wal_senders = 3
wal_keep_segments = 32
hot_standby = on
hot_standby_feedback = on
