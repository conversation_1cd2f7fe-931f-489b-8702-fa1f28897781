server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Application logs
  - job_name: ai-platform-backend
    static_configs:
      - targets:
          - localhost
        labels:
          job: ai-platform-backend
          __path__: /var/log/backend/*.log

  - job_name: ai-platform-frontend
    static_configs:
      - targets:
          - localhost
        labels:
          job: ai-platform-frontend
          __path__: /var/log/frontend/*.log

  - job_name: ai-platform-celery
    static_configs:
      - targets:
          - localhost
        labels:
          job: ai-platform-celery
          __path__: /var/log/celery/*.log

  # System logs
  - job_name: nginx
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx
          __path__: /var/log/nginx/*.log

  - job_name: postgres
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgres
          __path__: /var/log/postgresql/*.log

  - job_name: redis
    static_configs:
      - targets:
          - localhost
        labels:
          job: redis
          __path__: /var/log/redis/*.log

  # Docker container logs
  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          __path__: /var/lib/docker/containers/*/*.log
    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      - json:
          expressions:
            tag:
          source: attrs
      - regex:
          expression: (?P<container_name>(?:[^|]*))\|
          source: tag
      - timestamp:
          format: RFC3339Nano
          source: time
      - labels:
          stream:
          container_name:
      - output:
          source: output
