# Azure Terraform Variables

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "development"
}

variable "project_name" {
  description = "Project name"
  type        = string
  default     = "ai-data-platform"
}

variable "location" {
  description = "Azure region"
  type        = string
  default     = "East US"
}

variable "vnet_cidr" {
  description = "CIDR block for VNet"
  type        = string
  default     = "********/16"
}

variable "kubernetes_version" {
  description = "Kubernetes version"
  type        = string
  default     = "1.28"
}

variable "min_nodes" {
  description = "Minimum number of nodes"
  type        = number
  default     = 3
}

variable "max_nodes" {
  description = "Maximum number of nodes"
  type        = number
  default     = 10
}

variable "database_master_password" {
  description = "Master password for PostgreSQL"
  type        = string
  sensitive   = true
}

variable "backup_retention_days" {
  description = "Backup retention period"
  type        = number
  default     = 7
}

variable "cross_cloud_cidrs" {
  description = "CIDR blocks for cross-cloud access"
  type        = list(string)
  default     = ["10.0.0.0/16", "********/16"] # AWS and GCP CIDRs
}

variable "common_tags" {
  description = "Common tags for all resources"
  type        = map(string)
  default = {
    Project     = "ai-data-platform"
    ManagedBy   = "terraform"
    Environment = "development"
  }
}
