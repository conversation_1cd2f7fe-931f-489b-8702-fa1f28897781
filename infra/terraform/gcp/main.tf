# GCP Infrastructure for AI Data Platform
# Enterprise-grade multi-region deployment with GKE, Cloud SQL, Memorystore, and more

terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 4.0"
    }
  }
}

# Data sources
data "google_client_config" "default" {}

# Local values
locals {
  name_prefix = "ai-platform-${var.environment}"
  
  common_labels = {
    project     = "ai-data-platform"
    environment = var.environment
    managed-by  = "terraform"
    region      = var.region
  }
}

# Enable required APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "compute.googleapis.com",
    "container.googleapis.com",
    "sqladmin.googleapis.com",
    "redis.googleapis.com",
    "storage.googleapis.com",
    "cloudkms.googleapis.com",
    "secretmanager.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "cloudbuild.googleapis.com",
    "artifactregistry.googleapis.com",
    "aiplatform.googleapis.com",
    "bigquery.googleapis.com",
    "dataflow.googleapis.com",
    "pubsub.googleapis.com",
    "cloudscheduler.googleapis.com",
    "cloudfunctions.googleapis.com",
    "run.googleapis.com"
  ])
  
  project = var.project_id
  service = each.value
  
  disable_dependent_services = false
}

# VPC Network
resource "google_compute_network" "main" {
  name                    = "${local.name_prefix}-vpc"
  auto_create_subnetworks = false
  mtu                     = 1460
  
  depends_on = [google_project_service.required_apis]
}

# Subnets
resource "google_compute_subnetwork" "gke" {
  name          = "${local.name_prefix}-gke-subnet"
  ip_cidr_range = cidrsubnet(var.vpc_cidr, 8, 1)
  region        = var.region
  network       = google_compute_network.main.id
  
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = cidrsubnet(var.vpc_cidr, 4, 1)
  }
  
  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = cidrsubnet(var.vpc_cidr, 8, 2)
  }
  
  private_ip_google_access = true
}

resource "google_compute_subnetwork" "database" {
  name          = "${local.name_prefix}-database-subnet"
  ip_cidr_range = cidrsubnet(var.vpc_cidr, 8, 3)
  region        = var.region
  network       = google_compute_network.main.id
  
  private_ip_google_access = true
}

# Cloud Router and NAT
resource "google_compute_router" "main" {
  name    = "${local.name_prefix}-router"
  region  = var.region
  network = google_compute_network.main.id
}

resource "google_compute_router_nat" "main" {
  name                               = "${local.name_prefix}-nat"
  router                             = google_compute_router.main.name
  region                             = var.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  
  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

# Firewall Rules
resource "google_compute_firewall" "allow_internal" {
  name    = "${local.name_prefix}-allow-internal"
  network = google_compute_network.main.name
  
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  
  allow {
    protocol = "icmp"
  }
  
  source_ranges = [var.vpc_cidr]
}

resource "google_compute_firewall" "allow_cross_cloud" {
  count = length(var.cross_cloud_cidrs)
  
  name    = "${local.name_prefix}-allow-cross-cloud-${count.index}"
  network = google_compute_network.main.name
  
  allow {
    protocol = "tcp"
    ports    = ["22", "80", "443", "5432", "6379"]
  }
  
  source_ranges = [var.cross_cloud_cidrs[count.index]]
}

resource "google_compute_firewall" "allow_health_checks" {
  name    = "${local.name_prefix}-allow-health-checks"
  network = google_compute_network.main.name
  
  allow {
    protocol = "tcp"
    ports    = ["80", "443", "8080"]
  }
  
  source_ranges = [
    "***********/22",
    "**********/16"
  ]
  
  target_tags = ["gke-node"]
}

# Global Load Balancer
resource "google_compute_global_address" "main" {
  name = "${local.name_prefix}-global-ip"
}

resource "google_compute_managed_ssl_certificate" "main" {
  name = "${local.name_prefix}-ssl-cert"
  
  managed {
    domains = [var.domain_name, "api.${var.domain_name}"]
  }
}

resource "google_compute_url_map" "main" {
  name            = "${local.name_prefix}-url-map"
  default_service = google_compute_backend_service.main.id
  
  host_rule {
    hosts        = [var.domain_name, "api.${var.domain_name}"]
    path_matcher = "allpaths"
  }
  
  path_matcher {
    name            = "allpaths"
    default_service = google_compute_backend_service.main.id
    
    path_rule {
      paths   = ["/api/*"]
      service = google_compute_backend_service.api.id
    }
    
    path_rule {
      paths   = ["/*"]
      service = google_compute_backend_service.main.id
    }
  }
}

resource "google_compute_target_https_proxy" "main" {
  name             = "${local.name_prefix}-https-proxy"
  url_map          = google_compute_url_map.main.id
  ssl_certificates = [google_compute_managed_ssl_certificate.main.id]
}

resource "google_compute_global_forwarding_rule" "main" {
  name       = "${local.name_prefix}-forwarding-rule"
  target     = google_compute_target_https_proxy.main.id
  port_range = "443"
  ip_address = google_compute_global_address.main.address
}

# Backend Services
resource "google_compute_backend_service" "main" {
  name                  = "${local.name_prefix}-backend-service"
  protocol              = "HTTP"
  port_name             = "http"
  load_balancing_scheme = "EXTERNAL"
  timeout_sec           = 30
  
  health_checks = [google_compute_health_check.main.id]
  
  backend {
    group           = google_container_node_pool.general.instance_group_urls[0]
    balancing_mode  = "UTILIZATION"
    capacity_scaler = 1.0
  }
  
  cdn_policy {
    cache_mode                   = "CACHE_ALL_STATIC"
    signed_url_cache_max_age_sec = 7200
  }
  
  iap {
    oauth2_client_id     = var.iap_oauth2_client_id
    oauth2_client_secret = var.iap_oauth2_client_secret
  }
}

resource "google_compute_backend_service" "api" {
  name                  = "${local.name_prefix}-api-backend-service"
  protocol              = "HTTP"
  port_name             = "http"
  load_balancing_scheme = "EXTERNAL"
  timeout_sec           = 60
  
  health_checks = [google_compute_health_check.api.id]
  
  backend {
    group           = google_container_node_pool.general.instance_group_urls[0]
    balancing_mode  = "UTILIZATION"
    capacity_scaler = 1.0
  }
}

# Health Checks
resource "google_compute_health_check" "main" {
  name = "${local.name_prefix}-health-check"
  
  timeout_sec        = 5
  check_interval_sec = 10
  
  http_health_check {
    port         = 80
    request_path = "/health"
  }
}

resource "google_compute_health_check" "api" {
  name = "${local.name_prefix}-api-health-check"
  
  timeout_sec        = 5
  check_interval_sec = 10
  
  http_health_check {
    port         = 8000
    request_path = "/health"
  }
}

# GKE Cluster
resource "google_container_cluster" "main" {
  name     = "${local.name_prefix}-cluster"
  location = var.region
  
  # We can't create a cluster with no node pool defined, but we want to only use
  # separately managed node pools. So we create the smallest possible default
  # node pool and immediately delete it.
  remove_default_node_pool = true
  initial_node_count       = 1
  
  network    = google_compute_network.main.name
  subnetwork = google_compute_subnetwork.gke.name
  
  # Enable Autopilot for advanced features
  enable_autopilot = false
  
  # Networking configuration
  ip_allocation_policy {
    cluster_secondary_range_name  = "gke-pods"
    services_secondary_range_name = "gke-services"
  }
  
  # Security configuration
  network_policy {
    enabled = true
  }
  
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = false
    master_ipv4_cidr_block  = "**********/28"
  }
  
  # Workload Identity
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }
  
  # Addons
  addons_config {
    http_load_balancing {
      disabled = false
    }
    
    horizontal_pod_autoscaling {
      disabled = false
    }
    
    network_policy_config {
      disabled = false
    }
    
    gcp_filestore_csi_driver_config {
      enabled = true
    }
    
    gcs_fuse_csi_driver_config {
      enabled = true
    }
  }
  
  # Logging and monitoring
  logging_service    = "logging.googleapis.com/kubernetes"
  monitoring_service = "monitoring.googleapis.com/kubernetes"
  
  # Maintenance policy
  maintenance_policy {
    recurring_window {
      start_time = "2023-01-01T02:00:00Z"
      end_time   = "2023-01-01T06:00:00Z"
      recurrence = "FREQ=WEEKLY;BYDAY=SA"
    }
  }
  
  # Resource labels
  resource_labels = local.common_labels
  
  depends_on = [
    google_project_service.required_apis,
    google_compute_subnetwork.gke
  ]
}
