# GCP Terraform Outputs

# Project Information
output "project_id" {
  description = "GCP project ID"
  value       = var.project_id
}

output "region" {
  description = "GCP region"
  value       = var.region
}

# VPC Network
output "vpc_name" {
  description = "Name of the VPC network"
  value       = google_compute_network.main.name
}

output "vpc_id" {
  description = "ID of the VPC network"
  value       = google_compute_network.main.id
}

output "gke_subnet_name" {
  description = "Name of the GKE subnet"
  value       = google_compute_subnetwork.gke.name
}

output "database_subnet_name" {
  description = "Name of the database subnet"
  value       = google_compute_subnetwork.database.name
}

# GKE Cluster
output "gke_cluster_name" {
  description = "Name of the GKE cluster"
  value       = google_container_cluster.main.name
}

output "gke_cluster_endpoint" {
  description = "Endpoint for GKE control plane"
  value       = google_container_cluster.main.endpoint
  sensitive   = true
}

output "gke_cluster_ca_certificate" {
  description = "Cluster CA certificate (base64 encoded)"
  value       = google_container_cluster.main.master_auth[0].cluster_ca_certificate
  sensitive   = true
}

output "gke_cluster_location" {
  description = "Location of the GKE cluster"
  value       = google_container_cluster.main.location
}

# Cloud SQL
output "sql_instance_name" {
  description = "Name of the Cloud SQL instance"
  value       = google_sql_database_instance.main.name
}

output "sql_connection_name" {
  description = "Connection name of the Cloud SQL instance"
  value       = google_sql_database_instance.main.connection_name
  sensitive   = true
}

output "sql_private_ip" {
  description = "Private IP address of the Cloud SQL instance"
  value       = google_sql_database_instance.main.private_ip_address
  sensitive   = true
}

output "sql_database_name" {
  description = "Name of the database"
  value       = google_sql_database.main.name
}

output "sql_username" {
  description = "Username for Cloud SQL"
  value       = google_sql_user.main.name
  sensitive   = true
}

# Redis
output "redis_host" {
  description = "Host of the Redis instance"
  value       = google_redis_instance.main.host
  sensitive   = true
}

output "redis_port" {
  description = "Port of the Redis instance"
  value       = google_redis_instance.main.port
}

output "redis_auth_string" {
  description = "Auth string for Redis"
  value       = google_redis_instance.main.auth_string
  sensitive   = true
}

# Cloud Storage
output "storage_bucket_name" {
  description = "Name of the storage bucket"
  value       = google_storage_bucket.data_lake.name
}

output "storage_bucket_url" {
  description = "URL of the storage bucket"
  value       = google_storage_bucket.data_lake.url
}

# Service Account
output "gke_service_account_email" {
  description = "Email of the GKE service account"
  value       = google_service_account.gke_nodes.email
}

# Load Balancer (if using external load balancer)
output "load_balancer_ip" {
  description = "External IP for load balancer"
  value       = "TBD" # This would be set up with Kubernetes ingress
}

# Connection Strings
output "database_url" {
  description = "Database connection URL"
  value       = "postgresql://${google_sql_user.main.name}:${var.database_master_password}@${google_sql_database_instance.main.private_ip_address}:5432/${google_sql_database.main.name}"
  sensitive   = true
}

output "redis_url" {
  description = "Redis connection URL"
  value       = "redis://:${google_redis_instance.main.auth_string}@${google_redis_instance.main.host}:${google_redis_instance.main.port}"
  sensitive   = true
}

# Kubernetes Config
output "kubernetes_config" {
  description = "Kubernetes configuration for connecting to the cluster"
  value = {
    host                   = "https://${google_container_cluster.main.endpoint}"
    cluster_ca_certificate = base64decode(google_container_cluster.main.master_auth[0].cluster_ca_certificate)
    token                  = data.google_client_config.default.access_token
  }
  sensitive = true
}
