# Multi-Cloud Global Infrastructure
# This configuration sets up the global resources across AWS, Azure, and GCP

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
  }
  
  backend "s3" {
    bucket         = "ai-platform-terraform-state"
    key            = "global/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "ai-platform-terraform-locks"
  }
}

# Provider configurations
provider "aws" {
  region = var.aws_primary_region
  
  default_tags {
    tags = {
      Project     = "ai-data-platform"
      Environment = var.environment
      ManagedBy   = "terraform"
      Owner       = "platform-team"
    }
  }
}

provider "aws" {
  alias  = "secondary"
  region = var.aws_secondary_region
  
  default_tags {
    tags = {
      Project     = "ai-data-platform"
      Environment = var.environment
      ManagedBy   = "terraform"
      Owner       = "platform-team"
    }
  }
}

provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
    key_vault {
      purge_soft_delete_on_destroy    = true
      recover_soft_deleted_key_vaults = true
    }
  }
}

provider "google" {
  project = var.gcp_project_id
  region  = var.gcp_primary_region
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

# Local values
locals {
  common_tags = {
    Project     = "ai-data-platform"
    Environment = var.environment
    ManagedBy   = "terraform"
    Owner       = "platform-team"
  }
  
  regions = {
    aws_primary   = var.aws_primary_region
    aws_secondary = var.aws_secondary_region
    azure_primary = var.azure_primary_region
    gcp_primary   = var.gcp_primary_region
  }
}

# Global DNS and CDN (Cloudflare)
resource "cloudflare_zone" "main" {
  zone = var.domain_name
  plan = "pro"
}

resource "cloudflare_zone_settings_override" "main" {
  zone_id = cloudflare_zone.main.id
  
  settings {
    ssl                      = "strict"
    always_use_https         = "on"
    min_tls_version          = "1.2"
    opportunistic_encryption = "on"
    tls_1_3                  = "zrt"
    automatic_https_rewrites = "on"
    security_level           = "medium"
    challenge_ttl            = 1800
    browser_check            = "on"
    hotlink_protection       = "on"
    ip_geolocation           = "on"
    email_obfuscation        = "on"
    server_side_exclude      = "on"
    rocket_loader            = "on"
    minify {
      css  = "on"
      js   = "on"
      html = "on"
    }
    brotli = "on"
  }
}

# Global Load Balancer
resource "cloudflare_load_balancer_pool" "aws_primary" {
  name = "aws-primary-pool"
  
  origins {
    name    = "aws-primary"
    address = module.aws_primary.load_balancer_dns
    enabled = true
    weight  = 1
  }
  
  latitude  = 37.7749
  longitude = -122.4194
  
  monitor = cloudflare_load_balancer_monitor.main.id
}

resource "cloudflare_load_balancer_pool" "azure_primary" {
  name = "azure-primary-pool"
  
  origins {
    name    = "azure-primary"
    address = module.azure_primary.load_balancer_dns
    enabled = true
    weight  = 1
  }
  
  latitude  = 52.3676
  longitude = 4.9041
  
  monitor = cloudflare_load_balancer_monitor.main.id
}

resource "cloudflare_load_balancer_pool" "gcp_primary" {
  name = "gcp-primary-pool"
  
  origins {
    name    = "gcp-primary"
    address = module.gcp_primary.load_balancer_dns
    enabled = true
    weight  = 1
  }
  
  latitude  = 35.6762
  longitude = 139.6503
  
  monitor = cloudflare_load_balancer_monitor.main.id
}

resource "cloudflare_load_balancer_monitor" "main" {
  expected_codes = "200"
  method         = "GET"
  timeout        = 7
  path           = "/health"
  interval       = 60
  retries        = 2
  description    = "AI Platform Health Check"
  
  header {
    header = "Host"
    values = [var.domain_name]
  }
}

resource "cloudflare_load_balancer" "main" {
  zone_id          = cloudflare_zone.main.id
  name             = "ai-platform-global-lb"
  fallback_pool_id = cloudflare_load_balancer_pool.aws_primary.id
  
  default_pool_ids = [
    cloudflare_load_balancer_pool.aws_primary.id,
    cloudflare_load_balancer_pool.azure_primary.id,
    cloudflare_load_balancer_pool.gcp_primary.id,
  ]
  
  description = "Global load balancer for AI Data Platform"
  ttl         = 30
  proxied     = true
  
  # Geo steering
  region_pools {
    region = "WNAM"  # Western North America
    pool_ids = [
      cloudflare_load_balancer_pool.aws_primary.id,
      cloudflare_load_balancer_pool.gcp_primary.id,
    ]
  }
  
  region_pools {
    region = "ENAM"  # Eastern North America
    pool_ids = [
      cloudflare_load_balancer_pool.aws_primary.id,
      cloudflare_load_balancer_pool.azure_primary.id,
    ]
  }
  
  region_pools {
    region = "WEU"   # Western Europe
    pool_ids = [
      cloudflare_load_balancer_pool.azure_primary.id,
      cloudflare_load_balancer_pool.aws_primary.id,
    ]
  }
  
  region_pools {
    region = "APAC"  # Asia Pacific
    pool_ids = [
      cloudflare_load_balancer_pool.gcp_primary.id,
      cloudflare_load_balancer_pool.aws_primary.id,
    ]
  }
}

# DNS Records
resource "cloudflare_record" "main" {
  zone_id = cloudflare_zone.main.id
  name    = "@"
  value   = cloudflare_load_balancer.main.id
  type    = "CNAME"
  proxied = true
}

resource "cloudflare_record" "api" {
  zone_id = cloudflare_zone.main.id
  name    = "api"
  value   = cloudflare_load_balancer.main.id
  type    = "CNAME"
  proxied = true
}

resource "cloudflare_record" "www" {
  zone_id = cloudflare_zone.main.id
  name    = "www"
  value   = var.domain_name
  type    = "CNAME"
  proxied = true
}

# WAF Rules
resource "cloudflare_ruleset" "waf" {
  zone_id     = cloudflare_zone.main.id
  name        = "AI Platform WAF"
  description = "Web Application Firewall rules for AI Platform"
  kind        = "zone"
  phase       = "http_request_firewall_custom"
  
  rules {
    action = "block"
    expression = "(http.request.uri.path contains \"/admin\" and ip.geoip.country ne \"US\")"
    description = "Block admin access from outside US"
    enabled = true
  }
  
  rules {
    action = "challenge"
    expression = "(cf.threat_score gt 14)"
    description = "Challenge suspicious traffic"
    enabled = true
  }
  
  rules {
    action = "block"
    expression = "(http.request.method eq \"POST\" and rate(5m) gt 100)"
    description = "Rate limit POST requests"
    enabled = true
  }
}

# Cross-cloud VPN connections
resource "aws_vpn_gateway" "main" {
  provider = aws
  vpc_id   = module.aws_primary.vpc_id

  tags = merge(local.common_tags, {
    Name = "ai-platform-vpn-gateway"
  })
}

# Global secrets management
resource "aws_secretsmanager_secret" "cross_cloud_secrets" {
  provider                = aws
  name                    = "ai-platform/cross-cloud-secrets"
  description             = "Cross-cloud shared secrets"
  recovery_window_in_days = 7

  replica {
    region = var.aws_secondary_region
  }
}

resource "aws_secretsmanager_secret_version" "cross_cloud_secrets" {
  provider  = aws
  secret_id = aws_secretsmanager_secret.cross_cloud_secrets.id
  secret_string = jsonencode({
    database_master_password = var.database_master_password
    redis_auth_token        = var.redis_auth_token
    jwt_secret_key          = var.jwt_secret_key
    encryption_key          = var.encryption_key
  })
}

# Module calls for each cloud provider
module "aws_primary" {
  source = "../aws"

  environment = var.environment
  region      = var.aws_primary_region

  # Multi-cloud specific variables
  is_primary_region = true
  cross_cloud_vpc_cidrs = [
    var.azure_vnet_cidr,
    var.gcp_vpc_cidr
  ]

  # Shared secrets
  secrets_manager_arn = aws_secretsmanager_secret.cross_cloud_secrets.arn

  # Database configuration
  database_config = {
    instance_class    = "db.r6g.xlarge"
    allocated_storage = 1000
    multi_az         = true
    backup_retention = 30
  }

  # EKS configuration
  eks_config = {
    node_groups = {
      general = {
        instance_types = ["m6i.xlarge"]
        min_size      = 3
        max_size      = 20
        desired_size  = 6
      }
      ml_workloads = {
        instance_types = ["p3.2xlarge"]
        min_size      = 0
        max_size      = 10
        desired_size  = 2
      }
    }
  }
}

module "azure_primary" {
  source = "../azure"

  environment = var.environment
  location    = var.azure_primary_region

  # Multi-cloud specific variables
  is_primary_region = true
  cross_cloud_cidrs = [
    var.aws_vpc_cidr,
    var.gcp_vpc_cidr
  ]

  # Database configuration
  database_config = {
    sku_name                     = "GP_Standard_D4s_v3"
    storage_mb                   = 1048576  # 1TB
    backup_retention_days        = 30
    geo_redundant_backup_enabled = true
  }

  # AKS configuration
  aks_config = {
    node_pools = {
      general = {
        vm_size    = "Standard_D4s_v3"
        min_count  = 3
        max_count  = 20
        node_count = 6
      }
      ml_workloads = {
        vm_size    = "Standard_NC6s_v3"
        min_count  = 0
        max_count  = 10
        node_count = 2
      }
    }
  }
}

module "gcp_primary" {
  source = "../gcp"

  environment = var.environment
  region      = var.gcp_primary_region
  project_id  = var.gcp_project_id

  # Multi-cloud specific variables
  is_primary_region = true
  cross_cloud_cidrs = [
    var.aws_vpc_cidr,
    var.azure_vnet_cidr
  ]

  # Database configuration
  database_config = {
    tier               = "db-custom-4-16384"
    disk_size          = 1000
    backup_enabled     = true
    point_in_time_recovery_enabled = true
  }

  # GKE configuration
  gke_config = {
    node_pools = {
      general = {
        machine_type = "e2-standard-4"
        min_count    = 3
        max_count    = 20
        node_count   = 6
      }
      ml_workloads = {
        machine_type = "n1-standard-4"
        accelerator = {
          type  = "nvidia-tesla-t4"
          count = 1
        }
        min_count  = 0
        max_count  = 10
        node_count = 2
      }
    }
  }
}
