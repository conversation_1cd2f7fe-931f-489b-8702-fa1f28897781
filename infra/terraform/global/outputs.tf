# Global Terraform Outputs for AI Data Platform

# Global Configuration
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "project_name" {
  description = "Project name"
  value       = var.project_name
}

output "global_domain" {
  description = "Global domain name"
  value       = var.domain_name
}

# AWS Outputs
output "aws_primary_region" {
  description = "AWS primary region"
  value       = var.aws_primary_region
}

output "aws_vpc_id" {
  description = "AWS VPC ID"
  value       = try(module.aws_primary.vpc_id, null)
}

output "aws_eks_cluster_name" {
  description = "AWS EKS cluster name"
  value       = try(module.aws_primary.eks_cluster_name, null)
}

output "aws_eks_cluster_endpoint" {
  description = "AWS EKS cluster endpoint"
  value       = try(module.aws_primary.eks_cluster_endpoint, null)
  sensitive   = true
}

output "aws_rds_endpoint" {
  description = "AWS RDS endpoint"
  value       = try(module.aws_primary.rds_endpoint, null)
  sensitive   = true
}

output "aws_elasticache_endpoint" {
  description = "AWS ElastiCache endpoint"
  value       = try(module.aws_primary.elasticache_endpoint, null)
  sensitive   = true
}

output "aws_s3_bucket_data_lake" {
  description = "AWS S3 data lake bucket"
  value       = try(module.aws_primary.s3_bucket_data_lake, null)
}

output "aws_load_balancer_dns" {
  description = "AWS load balancer DNS name"
  value       = try(module.aws_primary.load_balancer_dns, null)
}

# Azure Outputs
output "azure_primary_region" {
  description = "Azure primary region"
  value       = var.azure_primary_region
}

output "azure_resource_group" {
  description = "Azure resource group name"
  value       = try(module.azure_primary.resource_group_name, null)
}

output "azure_vnet_id" {
  description = "Azure VNet ID"
  value       = try(module.azure_primary.vnet_id, null)
}

output "azure_aks_cluster_name" {
  description = "Azure AKS cluster name"
  value       = try(module.azure_primary.aks_cluster_name, null)
}

output "azure_aks_cluster_fqdn" {
  description = "Azure AKS cluster FQDN"
  value       = try(module.azure_primary.aks_cluster_fqdn, null)
  sensitive   = true
}

output "azure_postgresql_fqdn" {
  description = "Azure PostgreSQL FQDN"
  value       = try(module.azure_primary.postgresql_fqdn, null)
  sensitive   = true
}

output "azure_redis_hostname" {
  description = "Azure Redis hostname"
  value       = try(module.azure_primary.redis_hostname, null)
  sensitive   = true
}

output "azure_storage_account_name" {
  description = "Azure storage account name"
  value       = try(module.azure_primary.storage_account_name, null)
}

output "azure_load_balancer_dns" {
  description = "Azure load balancer DNS name"
  value       = try(module.azure_primary.load_balancer_dns, null)
}

# GCP Outputs
output "gcp_project_id" {
  description = "GCP project ID"
  value       = var.gcp_project_id
}

output "gcp_primary_region" {
  description = "GCP primary region"
  value       = var.gcp_primary_region
}

output "gcp_vpc_name" {
  description = "GCP VPC name"
  value       = try(module.gcp_primary.vpc_name, null)
}

output "gcp_gke_cluster_name" {
  description = "GCP GKE cluster name"
  value       = try(module.gcp_primary.gke_cluster_name, null)
}

output "gcp_gke_cluster_endpoint" {
  description = "GCP GKE cluster endpoint"
  value       = try(module.gcp_primary.gke_cluster_endpoint, null)
  sensitive   = true
}

output "gcp_sql_instance_name" {
  description = "GCP Cloud SQL instance name"
  value       = try(module.gcp_primary.sql_instance_name, null)
}

output "gcp_sql_connection_name" {
  description = "GCP Cloud SQL connection name"
  value       = try(module.gcp_primary.sql_connection_name, null)
  sensitive   = true
}

output "gcp_redis_host" {
  description = "GCP Memorystore Redis host"
  value       = try(module.gcp_primary.redis_host, null)
  sensitive   = true
}

output "gcp_storage_bucket_name" {
  description = "GCP storage bucket name"
  value       = try(module.gcp_primary.storage_bucket_name, null)
}

output "gcp_load_balancer_ip" {
  description = "GCP load balancer IP"
  value       = try(module.gcp_primary.load_balancer_ip, null)
}

# Cloudflare Outputs
output "cloudflare_zone_id" {
  description = "Cloudflare zone ID"
  value       = cloudflare_zone.main.id
  sensitive   = true
}

output "cloudflare_load_balancer_hostname" {
  description = "Cloudflare load balancer hostname"
  value       = cloudflare_load_balancer.main.hostname
}

output "cloudflare_nameservers" {
  description = "Cloudflare nameservers"
  value       = cloudflare_zone.main.name_servers
}

# Security Outputs
output "secrets_manager_arn" {
  description = "AWS Secrets Manager ARN"
  value       = aws_secretsmanager_secret.cross_cloud_secrets.arn
  sensitive   = true
}

# Monitoring Outputs
output "monitoring_endpoints" {
  description = "Monitoring service endpoints"
  value = {
    prometheus = "https://prometheus.${var.domain_name}"
    grafana    = "https://grafana.${var.domain_name}"
    jaeger     = "https://jaeger.${var.domain_name}"
    kibana     = "https://kibana.${var.domain_name}"
  }
}

# Application Endpoints
output "application_endpoints" {
  description = "Application service endpoints"
  value = {
    frontend = "https://${var.domain_name}"
    api      = "https://api.${var.domain_name}"
    docs     = "https://api.${var.domain_name}/docs"
    admin    = "https://admin.${var.domain_name}"
  }
}

# Management Endpoints
output "management_endpoints" {
  description = "Management service endpoints"
  value = {
    argocd = "https://argocd.${var.domain_name}"
    vault  = "https://vault.${var.domain_name}"
    kiali  = "https://kiali.${var.domain_name}"
  }
}

# Database Connection Strings (for applications)
output "database_connections" {
  description = "Database connection information"
  value = {
    aws_postgres = try("postgresql://username:password@${module.aws_primary.rds_endpoint}:5432/ai_platform", null)
    azure_postgres = try("postgresql://username:password@${module.azure_primary.postgresql_fqdn}:5432/ai_platform", null)
    gcp_postgres = try("postgresql://username:password@${module.gcp_primary.sql_private_ip}:5432/ai_platform", null)
  }
  sensitive = true
}

# Cache Connection Strings
output "cache_connections" {
  description = "Cache connection information"
  value = {
    aws_redis = try("redis://${module.aws_primary.elasticache_endpoint}:6379", null)
    azure_redis = try("redis://${module.azure_primary.redis_hostname}:6380", null)
    gcp_redis = try("redis://${module.gcp_primary.redis_host}:6379", null)
  }
  sensitive = true
}

# Kubernetes Cluster Information
output "kubernetes_clusters" {
  description = "Kubernetes cluster information"
  value = {
    aws = {
      cluster_name = try(module.aws_primary.eks_cluster_name, null)
      endpoint     = try(module.aws_primary.eks_cluster_endpoint, null)
      region       = var.aws_primary_region
    }
    azure = {
      cluster_name = try(module.azure_primary.aks_cluster_name, null)
      fqdn         = try(module.azure_primary.aks_cluster_fqdn, null)
      region       = var.azure_primary_region
    }
    gcp = {
      cluster_name = try(module.gcp_primary.gke_cluster_name, null)
      endpoint     = try(module.gcp_primary.gke_cluster_endpoint, null)
      region       = var.gcp_primary_region
    }
  }
  sensitive = true
}

# Deployment Information
output "deployment_info" {
  description = "Deployment information"
  value = {
    timestamp           = timestamp()
    terraform_version   = "1.6.0"
    kubernetes_version  = var.kubernetes_version
    environment         = var.environment
    multi_cloud_enabled = true
    compliance_enabled  = var.enable_compliance_logging
    monitoring_enabled  = var.enable_monitoring
    dr_enabled         = var.enable_disaster_recovery
  }
}
