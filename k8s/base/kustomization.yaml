apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: ai-platform-base

# Common labels applied to all resources
commonLabels:
  app.kubernetes.io/name: ai-data-platform
  app.kubernetes.io/version: "1.0.0"
  app.kubernetes.io/managed-by: kustomize

# Namespace for all resources
namespace: ai-platform

# Resources to include
resources:
  - namespace.yaml
  - ../components/backend
  - ../components/frontend
  - ../components/database
  - ../components/monitoring

# ConfigMap generators
configMapGenerator:
  - name: app-config
    literals:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - METRICS_ENABLED=true
      - TRACING_ENABLED=true

# Secret generators
secretGenerator:
  - name: app-secrets
    literals:
      - DATABASE_PASSWORD=placeholder
      - REDIS_PASSWORD=placeholder
      - JWT_SECRET=placeholder
    type: Opaque

# Images to use
images:
  - name: ai-platform-backend
    newTag: latest
  - name: ai-platform-frontend
    newTag: latest

# Patches
patches:
  - target:
      kind: Deployment
      name: backend
    patch: |-
      - op: add
        path: /spec/template/spec/containers/0/env/-
        value:
          name: CLOUD_PROVIDER
          value: "multi-cloud"
  
  - target:
      kind: Deployment
      name: frontend
    patch: |-
      - op: add
        path: /spec/template/spec/containers/0/env/-
        value:
          name: NEXT_PUBLIC_CLOUD_PROVIDER
          value: "multi-cloud"

# Replicas
replicas:
  - name: backend
    count: 3
  - name: frontend
    count: 3
  - name: celery-worker
    count: 2

# Resource transformers
transformers:
  - resource-limits.yaml
