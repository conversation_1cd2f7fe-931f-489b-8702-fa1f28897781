apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  labels:
    app: backend
    component: api
data:
  # Application Configuration
  ENVIRONMENT: "production"
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  
  # API Configuration
  API_V1_PREFIX: "/api/v1"
  CORS_ORIGINS: "https://ai-platform.com,https://www.ai-platform.com"
  CORS_ALLOW_CREDENTIALS: "true"
  
  # Database Configuration
  DATABASE_POOL_SIZE: "20"
  DATABASE_MAX_OVERFLOW: "30"
  DATABASE_POOL_TIMEOUT: "30"
  DATABASE_POOL_RECYCLE: "3600"
  
  # Redis Configuration
  REDIS_MAX_CONNECTIONS: "20"
  REDIS_RETRY_ON_TIMEOUT: "true"
  REDIS_SOCKET_KEEPALIVE: "true"
  
  # Security Configuration
  ACCESS_TOKEN_EXPIRE_MINUTES: "30"
  REFRESH_TOKEN_EXPIRE_DAYS: "7"
  ALGORITHM: "HS256"
  
  # Rate Limiting
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  
  # Monitoring
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"
  JAEGER_AGENT_HOST: "jaeger-agent"
  JAEGER_AGENT_PORT: "6831"
  
  # ML Configuration
  ML_MODEL_CACHE_TTL: "3600"
  ML_BATCH_SIZE: "32"
  ML_MAX_WORKERS: "4"
  
  # File Upload
  MAX_FILE_SIZE: "100MB"
  ALLOWED_FILE_TYPES: "csv,json,parquet,xlsx"
  
  # Email Configuration
  EMAIL_ENABLED: "true"
  EMAIL_SMTP_PORT: "587"
  EMAIL_USE_TLS: "true"
  
  # Compliance
  AUDIT_LOG_ENABLED: "true"
  GDPR_COMPLIANCE_ENABLED: "true"
  HIPAA_COMPLIANCE_ENABLED: "true"
  SOC2_COMPLIANCE_ENABLED: "true"
