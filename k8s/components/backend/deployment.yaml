apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  labels:
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ai-data-platform
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/component: backend
  template:
    metadata:
      labels:
        app.kubernetes.io/component: backend
        app.kubernetes.io/part-of: ai-data-platform
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      containers:
        - name: backend
          image: ai-platform-backend:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8000
              protocol: TCP
            - name: metrics
              containerPort: 9090
              protocol: TCP
          env:
            - name: ENVIRONMENT
              value: "production"
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-credentials
                  key: url
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: url
            - name: SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: app-secrets
                  key: JWT_SECRET
            - name: CELERY_BROKER_URL
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: broker-url
            - name: CELERY_RESULT_BACKEND
              valueFrom:
                secretKeyRef:
                  name: redis-credentials
                  key: result-backend-url
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: external-api-keys
                  key: openai-api-key
                  optional: true
            - name: SENTRY_DSN
              valueFrom:
                secretKeyRef:
                  name: monitoring-secrets
                  key: sentry-dsn
                  optional: true
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          envFrom:
            - configMapRef:
                name: app-config
            - configMapRef:
                name: backend-config
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: app-data
              mountPath: /app/data
            - name: app-logs
              mountPath: /app/logs
      volumes:
        - name: tmp
          emptyDir: {}
        - name: app-data
          persistentVolumeClaim:
            claimName: backend-data
        - name: app-logs
          emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app.kubernetes.io/component
                      operator: In
                      values:
                        - backend
                topologyKey: kubernetes.io/hostname
      tolerations:
        - key: "kubernetes.io/arch"
          operator: "Equal"
          value: "amd64"
          effect: "NoSchedule"
      nodeSelector:
        kubernetes.io/os: linux
        kubernetes.io/arch: amd64

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backend
  labels:
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ai-data-platform
automountServiceAccountToken: true

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-data
  labels:
    app.kubernetes.io/component: backend
    app.kubernetes.io/part-of: ai-data-platform
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
