apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: backend-component

resources:
  - deployment.yaml
  - service.yaml
  - configmap.yaml
  - secret.yaml
  - hpa.yaml
  - pdb.yaml

commonLabels:
  app: backend
  version: v1.0.0
  tier: backend

images:
  - name: ai-platform-backend
    newName: ghcr.io/jainam1673/ai-data-platform-backend
    newTag: latest

configMapGenerator:
  - name: backend-env-config
    literals:
      - DEPLOYMENT_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
      - BUILD_VERSION=1.0.0
      - WORKER_PROCESSES=4
      - WORKER_CONNECTIONS=1024
      - CELERY_WORKER_CONCURRENCY=4
      - MAX_CONNECTIONS=100
      - CONNECTION_TIMEOUT=30

replicas:
  - name: backend-deployment
    count: 3
