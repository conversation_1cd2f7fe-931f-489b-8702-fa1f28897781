apiVersion: v1
kind: Secret
metadata:
  name: backend-secrets
  labels:
    app: backend
    component: api
type: Opaque
stringData:
  # Database Credentials
  DATABASE_URL: "*****************************************************/ai_platform"
  
  # Redis Credentials
  REDIS_URL: "redis://:CHANGE_ME@redis-service:6379/0"
  
  # JWT Secret
  SECRET_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # Encryption Keys
  ENCRYPTION_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # Email Credentials
  EMAIL_SMTP_HOST: "smtp.gmail.com"
  EMAIL_SMTP_USER: "<EMAIL>"
  EMAIL_SMTP_PASSWORD: "CHANGE_ME"
  
  # Cloud Provider Credentials
  AWS_ACCESS_KEY_ID: "CHANGE_ME"
  AWS_SECRET_ACCESS_KEY: "CHANGE_ME"
  AWS_REGION: "us-east-1"
  
  AZURE_CLIENT_ID: "CHANGE_ME"
  AZURE_CLIENT_SECRET: "CHANGE_ME"
  AZURE_TENANT_ID: "CHANGE_ME"
  
  GCP_SERVICE_ACCOUNT_KEY: "CHANGE_ME"
  GCP_PROJECT_ID: "CHANGE_ME"
  
  # External Service API Keys
  OPENAI_API_KEY: "CHANGE_ME"
  ANTHROPIC_API_KEY: "CHANGE_ME"
  
  # Monitoring
  SENTRY_DSN: "CHANGE_ME"
  
  # OAuth Secrets
  GOOGLE_CLIENT_SECRET: "CHANGE_ME"
  GITHUB_CLIENT_SECRET: "CHANGE_ME"
  MICROSOFT_CLIENT_SECRET: "CHANGE_ME"
