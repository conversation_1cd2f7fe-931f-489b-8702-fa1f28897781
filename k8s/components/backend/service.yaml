apiVersion: v1
kind: Service
metadata:
  name: backend-service
  labels:
    app: backend
    component: api
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8000
      protocol: TCP
      name: http
  selector:
    app: backend
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service-headless
  labels:
    app: backend
    component: api
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - port: 8000
      targetPort: 8000
      protocol: TCP
      name: http
  selector:
    app: backend
