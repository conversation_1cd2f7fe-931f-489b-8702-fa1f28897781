apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  labels:
    app: postgres
    component: database
data:
  postgresql.conf: |
    # PostgreSQL Configuration for AI Platform
    
    # Connection Settings
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    
    # Memory Settings
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB
    
    # WAL Settings
    wal_level = replica
    max_wal_size = 1GB
    min_wal_size = 80MB
    checkpoint_completion_target = 0.9
    
    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000
    log_checkpoints = on
    log_connections = on
    log_disconnections = on
    log_lock_waits = on
    
    # Performance
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # Autovacuum
    autovacuum = on
    autovacuum_max_workers = 3
    autovacuum_naptime = 1min
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  labels:
    app: redis
    component: cache
data:
  redis.conf: |
    # Redis Configuration for AI Platform
    
    # Network
    bind 0.0.0.0
    port 6379
    timeout 0
    tcp-keepalive 300
    
    # General
    daemonize no
    supervised no
    pidfile /var/run/redis_6379.pid
    loglevel notice
    logfile ""
    databases 16
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    stop-writes-on-bgsave-error yes
    rdbcompression yes
    rdbchecksum yes
    dbfilename dump.rdb
    dir /data
    
    # Security
    requirepass ${REDIS_PASSWORD}
    
    # Memory Management
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    
    # Append Only File
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    
    # Slow Log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Client Output Buffer Limits
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
