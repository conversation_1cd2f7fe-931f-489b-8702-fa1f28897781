FROM jupyter/scipy-notebook:latest

# Switch to root to install packages
USER root

# Install additional system packages
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Switch back to jovyan user
USER jovyan

# Install Python packages for ML/AI
RUN pip install --no-cache-dir \
    torch \
    torchvision \
    transformers \
    datasets \
    accelerate \
    autogluon \
    langchain \
    llama-index \
    openai \
    scikit-learn \
    xgboost \
    lightgbm \
    catboost \
    optuna \
    mlflow \
    wandb \
    plotly \
    seaborn \
    streamlit \
    gradio

# Set working directory
WORKDIR /home/<USER>/work

# Expose Jupyter port
EXPOSE 8888
