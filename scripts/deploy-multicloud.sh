#!/bin/bash

# Multi-Cloud AI Data Platform Deployment Script
# Deploys across AWS EKS, Azure AKS, and GCP GKE simultaneously
# Usage: ./scripts/deploy-multicloud.sh [environment] [version]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Cloud provider configurations
declare -A CLOUD_CONFIGS=(
    ["aws"]="us-east-1,us-west-2"
    ["azure"]="eastus,westeurope"
    ["gcp"]="us-central1,europe-west1"
)

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_cloud() {
    local cloud=$1
    local message=$2
    case $cloud in
        aws)
            echo -e "${YELLOW}[AWS]${NC} $message"
            ;;
        azure)
            echo -e "${CYAN}[AZURE]${NC} $message"
            ;;
        gcp)
            echo -e "${PURPLE}[GCP]${NC} $message"
            ;;
        *)
            echo -e "${BLUE}[$cloud]${NC} $message"
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking multi-cloud prerequisites..."
    
    # Check required tools
    local required_tools=("terraform" "kubectl" "helm" "aws" "az" "gcloud" "docker")
    for tool in "${required_tools[@]}"; do
        if ! command -v $tool &> /dev/null; then
            log_error "$tool is not installed"
            exit 1
        fi
    done
    
    # Check cloud authentication
    log_info "Verifying cloud authentication..."
    
    # AWS
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS authentication failed. Run 'aws configure' or set AWS credentials"
        exit 1
    fi
    log_success "AWS authentication verified"
    
    # Azure
    if ! az account show &> /dev/null; then
        log_error "Azure authentication failed. Run 'az login'"
        exit 1
    fi
    log_success "Azure authentication verified"
    
    # GCP
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        log_error "GCP authentication failed. Run 'gcloud auth login'"
        exit 1
    fi
    log_success "GCP authentication verified"
    
    # Check Terraform
    if [[ ! -f "$PROJECT_ROOT/infra/terraform/global/terraform.tfvars" ]]; then
        log_error "Terraform variables file not found. Create infra/terraform/global/terraform.tfvars"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Deploy infrastructure with Terraform
deploy_infrastructure() {
    log_info "Deploying multi-cloud infrastructure with Terraform..."
    
    cd "$PROJECT_ROOT/infra/terraform/global"
    
    # Initialize Terraform
    log_info "Initializing Terraform..."
    terraform init -upgrade
    
    # Plan deployment
    log_info "Planning infrastructure deployment..."
    terraform plan -var="environment=$ENVIRONMENT" -out=tfplan
    
    # Apply deployment
    log_info "Applying infrastructure deployment..."
    terraform apply tfplan
    
    # Get outputs
    log_info "Retrieving infrastructure outputs..."
    terraform output -json > "$PROJECT_ROOT/terraform-outputs.json"
    
    log_success "Infrastructure deployment completed"
}

# Configure kubectl for all clusters
configure_kubectl() {
    log_info "Configuring kubectl for all cloud clusters..."
    
    # AWS EKS
    log_cloud "aws" "Configuring EKS cluster access..."
    local aws_region=$(jq -r '.aws_primary_region.value' "$PROJECT_ROOT/terraform-outputs.json")
    local eks_cluster=$(jq -r '.aws_eks_cluster_name.value' "$PROJECT_ROOT/terraform-outputs.json")
    aws eks update-kubeconfig --region $aws_region --name $eks_cluster --alias aws-cluster
    
    # Azure AKS
    log_cloud "azure" "Configuring AKS cluster access..."
    local azure_rg=$(jq -r '.azure_resource_group.value' "$PROJECT_ROOT/terraform-outputs.json")
    local aks_cluster=$(jq -r '.azure_aks_cluster_name.value' "$PROJECT_ROOT/terraform-outputs.json")
    az aks get-credentials --resource-group $azure_rg --name $aks_cluster --context azure-cluster
    
    # GCP GKE
    log_cloud "gcp" "Configuring GKE cluster access..."
    local gcp_project=$(jq -r '.gcp_project_id.value' "$PROJECT_ROOT/terraform-outputs.json")
    local gcp_region=$(jq -r '.gcp_primary_region.value' "$PROJECT_ROOT/terraform-outputs.json")
    local gke_cluster=$(jq -r '.gcp_gke_cluster_name.value' "$PROJECT_ROOT/terraform-outputs.json")
    gcloud container clusters get-credentials $gke_cluster --region $gcp_region --project $gcp_project
    kubectl config rename-context $(kubectl config current-context) gcp-cluster
    
    log_success "Kubectl configured for all clusters"
}

# Install cluster prerequisites
install_cluster_prerequisites() {
    local cluster_context=$1
    local cloud_provider=$2
    
    log_cloud "$cloud_provider" "Installing cluster prerequisites..."
    
    kubectl config use-context $cluster_context
    
    # Install cert-manager
    log_cloud "$cloud_provider" "Installing cert-manager..."
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --version v1.13.0 \
        --set installCRDs=true \
        --wait
    
    # Install ingress controller
    case $cloud_provider in
        aws)
            log_cloud "aws" "Installing AWS Load Balancer Controller..."
            helm repo add eks https://aws.github.io/eks-charts
            helm upgrade --install aws-load-balancer-controller eks/aws-load-balancer-controller \
                --namespace kube-system \
                --set clusterName=$eks_cluster \
                --set serviceAccount.create=false \
                --set serviceAccount.name=aws-load-balancer-controller \
                --wait
            ;;
        azure)
            log_cloud "azure" "Installing Application Gateway Ingress Controller..."
            helm repo add application-gateway-kubernetes-ingress https://appgwingress.blob.core.windows.net/ingress-azure-helm-package/
            helm upgrade --install ingress-azure application-gateway-kubernetes-ingress/ingress-azure \
                --namespace default \
                --wait
            ;;
        gcp)
            log_cloud "gcp" "Installing GCP Ingress Controller..."
            # GKE uses built-in ingress controller
            kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-gce/master/deploy/gce-ingress-controller.yaml
            ;;
    esac
    
    # Install Istio service mesh
    log_cloud "$cloud_provider" "Installing Istio service mesh..."
    curl -L https://istio.io/downloadIstio | sh -
    export PATH="$PWD/istio-*/bin:$PATH"
    istioctl install --set values.defaultRevision=default -y
    kubectl label namespace default istio-injection=enabled --overwrite
    
    # Install monitoring stack
    log_cloud "$cloud_provider" "Installing monitoring stack..."
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add grafana https://grafana.github.io/helm-charts
    helm repo update
    
    # Prometheus
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=100Gi \
        --set grafana.adminPassword=admin123 \
        --wait
    
    # Install ArgoCD for GitOps
    log_cloud "$cloud_provider" "Installing ArgoCD..."
    kubectl create namespace argocd --dry-run=client -o yaml | kubectl apply -f -
    kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
    
    log_cloud "$cloud_provider" "Prerequisites installation completed"
}

# Deploy application to cluster
deploy_application() {
    local cluster_context=$1
    local cloud_provider=$2
    
    log_cloud "$cloud_provider" "Deploying AI Data Platform application..."
    
    kubectl config use-context $cluster_context
    
    # Apply cloud-specific overlay
    cd "$PROJECT_ROOT/k8s"
    kubectl apply -k "overlays/$cloud_provider"
    
    # Wait for deployment
    log_cloud "$cloud_provider" "Waiting for deployment to be ready..."
    kubectl wait --for=condition=available --timeout=600s deployment/backend -n ai-platform
    kubectl wait --for=condition=available --timeout=600s deployment/frontend -n ai-platform
    
    # Get service endpoints
    local backend_endpoint=$(kubectl get service backend -n ai-platform -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    local frontend_endpoint=$(kubectl get service frontend -n ai-platform -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    
    log_cloud "$cloud_provider" "Backend endpoint: $backend_endpoint"
    log_cloud "$cloud_provider" "Frontend endpoint: $frontend_endpoint"
    
    log_cloud "$cloud_provider" "Application deployment completed"
}

# Run health checks
run_health_checks() {
    local cluster_context=$1
    local cloud_provider=$2
    
    log_cloud "$cloud_provider" "Running health checks..."
    
    kubectl config use-context $cluster_context
    
    # Check pod status
    local unhealthy_pods=$(kubectl get pods -n ai-platform --field-selector=status.phase!=Running --no-headers | wc -l)
    if [[ $unhealthy_pods -gt 0 ]]; then
        log_warning "$cloud_provider has $unhealthy_pods unhealthy pods"
        kubectl get pods -n ai-platform --field-selector=status.phase!=Running
    else
        log_cloud "$cloud_provider" "All pods are healthy"
    fi
    
    # Check service endpoints
    local backend_ready=$(kubectl get endpoints backend -n ai-platform -o jsonpath='{.subsets[0].addresses[0].ip}' 2>/dev/null || echo "")
    if [[ -n "$backend_ready" ]]; then
        log_cloud "$cloud_provider" "Backend service is ready"
    else
        log_warning "$cloud_provider backend service is not ready"
    fi
    
    # Test application endpoints
    local service_ip=$(kubectl get service frontend -n ai-platform -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    if [[ -n "$service_ip" ]]; then
        if curl -f "http://$service_ip/health" &>/dev/null; then
            log_cloud "$cloud_provider" "Application health check passed"
        else
            log_warning "$cloud_provider application health check failed"
        fi
    fi
}

# Configure cross-cloud networking
configure_cross_cloud_networking() {
    log_info "Configuring cross-cloud networking and service mesh..."
    
    # Configure Istio multi-cluster
    log_info "Setting up Istio multi-cluster mesh..."
    
    # AWS cluster as primary
    kubectl config use-context aws-cluster
    kubectl apply -f - <<EOF
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: primary
spec:
  values:
    pilot:
      env:
        EXTERNAL_ISTIOD: true
        ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY: true
EOF
    
    # Create cross-cluster secrets
    kubectl create secret generic cacerts -n istio-system \
        --from-file=root-cert.pem \
        --from-file=cert-chain.pem \
        --from-file=ca-cert.pem \
        --from-file=ca-key.pem || true
    
    # Configure Azure and GCP as remote clusters
    for context in azure-cluster gcp-cluster; do
        kubectl config use-context $context
        kubectl apply -f - <<EOF
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: remote
spec:
  values:
    istiodRemote:
      enabled: true
    pilot:
      env:
        EXTERNAL_ISTIOD: true
    global:
      meshID: mesh1
      remotePilotAddress: \${DISCOVERY_ADDRESS}
EOF
    done
    
    log_success "Cross-cloud networking configured"
}

# Setup global load balancing
setup_global_load_balancing() {
    log_info "Setting up global load balancing with Cloudflare..."
    
    # This would typically involve:
    # 1. Configuring Cloudflare load balancer pools
    # 2. Setting up health checks
    # 3. Configuring geo-routing
    # 4. Setting up failover policies
    
    # For now, we'll just log the endpoints
    log_info "Collecting service endpoints for global load balancer..."
    
    for context in aws-cluster azure-cluster gcp-cluster; do
        kubectl config use-context $context
        local endpoint=$(kubectl get service frontend -n ai-platform -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
        log_info "$context endpoint: $endpoint"
    done
    
    log_success "Global load balancing setup completed"
}

# Main deployment function
main() {
    log_info "Starting multi-cloud deployment of AI Data Platform"
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    log_info "Clouds: AWS, Azure, GCP"
    
    # Pre-deployment checks
    check_prerequisites
    
    # Deploy infrastructure
    deploy_infrastructure
    
    # Configure cluster access
    configure_kubectl
    
    # Deploy to each cloud in parallel
    log_info "Deploying to all cloud providers in parallel..."
    
    # Install prerequisites on all clusters
    install_cluster_prerequisites "aws-cluster" "aws" &
    AWS_PID=$!
    
    install_cluster_prerequisites "azure-cluster" "azure" &
    AZURE_PID=$!
    
    install_cluster_prerequisites "gcp-cluster" "gcp" &
    GCP_PID=$!
    
    # Wait for all prerequisite installations
    wait $AWS_PID && log_success "AWS prerequisites completed" || log_error "AWS prerequisites failed"
    wait $AZURE_PID && log_success "Azure prerequisites completed" || log_error "Azure prerequisites failed"
    wait $GCP_PID && log_success "GCP prerequisites completed" || log_error "GCP prerequisites failed"
    
    # Deploy applications to all clusters
    deploy_application "aws-cluster" "aws" &
    AWS_APP_PID=$!
    
    deploy_application "azure-cluster" "azure" &
    AZURE_APP_PID=$!
    
    deploy_application "gcp-cluster" "gcp" &
    GCP_APP_PID=$!
    
    # Wait for all application deployments
    wait $AWS_APP_PID && log_success "AWS application deployed" || log_error "AWS application deployment failed"
    wait $AZURE_APP_PID && log_success "Azure application deployed" || log_error "Azure application deployment failed"
    wait $GCP_APP_PID && log_success "GCP application deployed" || log_error "GCP application deployment failed"
    
    # Configure cross-cloud features
    configure_cross_cloud_networking
    setup_global_load_balancing
    
    # Run health checks on all clusters
    log_info "Running health checks on all clusters..."
    run_health_checks "aws-cluster" "aws"
    run_health_checks "azure-cluster" "azure"
    run_health_checks "gcp-cluster" "gcp"
    
    # Final summary
    log_success "Multi-cloud deployment completed successfully!"
    echo
    log_info "Deployment Summary:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Version: $VERSION"
    echo "  AWS Region: $(jq -r '.aws_primary_region.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo "  Azure Region: $(jq -r '.azure_primary_region.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo "  GCP Region: $(jq -r '.gcp_primary_region.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo
    log_info "Global Access Points:"
    echo "  Primary: https://$(jq -r '.global_domain.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo "  API: https://api.$(jq -r '.global_domain.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo
    log_info "Monitoring Dashboards:"
    echo "  Grafana: https://grafana.$(jq -r '.global_domain.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo "  Prometheus: https://prometheus.$(jq -r '.global_domain.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo
    log_info "Management Tools:"
    echo "  ArgoCD: https://argocd.$(jq -r '.global_domain.value' "$PROJECT_ROOT/terraform-outputs.json")"
    echo "  Istio: https://kiali.$(jq -r '.global_domain.value' "$PROJECT_ROOT/terraform-outputs.json")"
}

# Run main function
main "$@"
