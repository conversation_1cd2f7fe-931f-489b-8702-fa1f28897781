#!/bin/bash

# AI Data Platform Deployment Script
# Usage: ./scripts/deploy.sh [environment] [version]
# Example: ./scripts/deploy.sh production v1.0.0

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate environment
validate_environment() {
    case $ENVIRONMENT in
        development|staging|production)
            log_info "Deploying to $ENVIRONMENT environment"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            log_info "Valid environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose is not installed"
        exit 1
    fi
    
    # Check if required environment files exist
    if [[ ! -f "$PROJECT_ROOT/.env.$ENVIRONMENT" ]]; then
        log_error "Environment file .env.$ENVIRONMENT not found"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Backup database (production only)
backup_database() {
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Creating database backup..."
        
        BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        
        docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump \
            -U postgres ai_platform_prod > "backups/$BACKUP_FILE"
        
        if [[ $? -eq 0 ]]; then
            log_success "Database backup created: $BACKUP_FILE"
        else
            log_error "Database backup failed"
            exit 1
        fi
    fi
}

# Build images
build_images() {
    log_info "Building Docker images..."
    
    # Set build arguments
    BUILD_ARGS=""
    if [[ "$ENVIRONMENT" == "production" ]]; then
        BUILD_ARGS="--no-cache"
    fi
    
    # Build backend image
    log_info "Building backend image..."
    docker build $BUILD_ARGS -t ai-platform-backend:$VERSION -f backend/Dockerfile.prod backend/
    
    # Build frontend image
    log_info "Building frontend image..."
    docker build $BUILD_ARGS -t ai-platform-frontend:$VERSION -f frontend/Dockerfile.prod frontend/
    
    log_success "Images built successfully"
}

# Deploy services
deploy_services() {
    log_info "Deploying services..."
    
    # Copy environment file
    cp ".env.$ENVIRONMENT" .env
    
    # Choose docker-compose file based on environment
    COMPOSE_FILE="docker-compose.yml"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        COMPOSE_FILE="docker-compose.prod.yml"
    fi
    
    # Stop existing services
    log_info "Stopping existing services..."
    docker-compose -f $COMPOSE_FILE down
    
    # Start new services
    log_info "Starting new services..."
    docker-compose -f $COMPOSE_FILE up -d
    
    log_success "Services deployed"
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    timeout 60 bash -c 'until docker-compose exec -T postgres pg_isready -U postgres; do sleep 2; done'
    
    # Run migrations
    docker-compose exec -T backend alembic upgrade head
    
    if [[ $? -eq 0 ]]; then
        log_success "Database migrations completed"
    else
        log_error "Database migrations failed"
        exit 1
    fi
}

# Health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    log_info "Checking backend health..."
    for i in {1..30}; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log_success "Backend is healthy"
            break
        fi
        if [[ $i -eq 30 ]]; then
            log_error "Backend health check failed"
            exit 1
        fi
        sleep 10
    done
    
    # Check frontend health
    log_info "Checking frontend health..."
    for i in {1..30}; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            log_success "Frontend is healthy"
            break
        fi
        if [[ $i -eq 30 ]]; then
            log_error "Frontend health check failed"
            exit 1
        fi
        sleep 10
    done
    
    # Run comprehensive health check
    log_info "Running comprehensive health check..."
    if curl -f http://localhost:8000/monitoring/health > /dev/null 2>&1; then
        log_success "All health checks passed"
    else
        log_warning "Some health checks failed, but services are running"
    fi
}

# Cleanup old images
cleanup_images() {
    log_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old versions (keep last 3)
    docker images ai-platform-backend --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi
    docker images ai-platform-frontend --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi
    
    log_success "Cleanup completed"
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Deployment $status: $message\"}" \
            "$SLACK_WEBHOOK_URL"
    fi
    
    log_info "Notification sent: $message"
}

# Rollback function
rollback() {
    log_warning "Rolling back deployment..."
    
    # Stop current services
    docker-compose down
    
    # Start previous version (assuming it's tagged as 'previous')
    docker-compose up -d
    
    log_warning "Rollback completed"
    send_notification "ROLLED BACK" "Deployment to $ENVIRONMENT was rolled back"
}

# Main deployment function
main() {
    log_info "Starting deployment to $ENVIRONMENT environment (version: $VERSION)"
    
    # Create necessary directories
    mkdir -p backups logs
    
    # Trap errors for rollback
    trap 'log_error "Deployment failed"; rollback; exit 1' ERR
    
    # Run deployment steps
    validate_environment
    check_prerequisites
    backup_database
    build_images
    deploy_services
    run_migrations
    run_health_checks
    cleanup_images
    
    log_success "Deployment completed successfully!"
    send_notification "SUCCESS" "Deployment to $ENVIRONMENT completed successfully"
    
    # Show deployment info
    echo
    log_info "Deployment Information:"
    echo "  Environment: $ENVIRONMENT"
    echo "  Version: $VERSION"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8000"
    echo "  API Docs: http://localhost:8000/docs"
    echo "  Monitoring: http://localhost:3001 (Grafana)"
    echo
    log_info "To view logs: docker-compose logs -f"
    log_info "To stop services: docker-compose down"
}

# Run main function
main "$@"
