#!/bin/bash

# AI Data Platform Development Setup Script
echo "🛠️  Setting up AI Data Platform for development..."

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.11+"
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 20+"
    exit 1
fi

# Check pnpm
if ! command -v pnpm &> /dev/null; then
    echo "📦 Installing pnpm..."
    npm install -g pnpm
fi

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker for database services."
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Setup backend
echo "🐍 Setting up backend..."
cd backend

# Create virtual environment
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Create environment file
if [ ! -f ".env" ]; then
    echo "Creating backend .env file..."
    cp .env.example .env
    echo "⚠️  Please update backend/.env with your configuration"
fi

cd ..

# Setup frontend
echo "⚛️  Setting up frontend..."
cd frontend

# Install dependencies
echo "Installing Node.js dependencies..."
pnpm install

# Create environment file
if [ ! -f ".env.local" ]; then
    echo "Creating frontend .env.local file..."
    cp .env.example .env.local
    echo "⚠️  Please update frontend/.env.local with your configuration"
fi

cd ..

# Setup database services
echo "🗄️  Starting database services..."
docker-compose up -d postgres redis

# Wait for services
echo "⏳ Waiting for database services to start..."
sleep 5

# Run database migrations
echo "🔄 Running database migrations..."
cd backend
source venv/bin/activate
alembic upgrade head
cd ..

# Create data directories
echo "📁 Creating data directories..."
mkdir -p data/uploads data/raw data/processed data/lake data/models data/cache/huggingface logs

echo ""
echo "✅ Development setup complete!"
echo ""
echo "🚀 To start development:"
echo ""
echo "Backend:"
echo "  cd backend"
echo "  source venv/bin/activate"
echo "  uvicorn app.main:app --reload"
echo ""
echo "Frontend:"
echo "  cd frontend"
echo "  pnpm dev"
echo ""
echo "Celery Worker:"
echo "  cd backend"
echo "  source venv/bin/activate"
echo "  celery -A app.workers.celery_app worker --loglevel=info"
echo ""
echo "🌐 Services will be available at:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "📊 Database services:"
echo "   PostgreSQL: localhost:5432"
echo "   Redis: localhost:6379"
echo ""
echo "⚠️  Don't forget to update the .env files with your configuration!"
