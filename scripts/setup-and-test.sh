#!/bin/bash

# AI Data Platform Complete Setup and Test Script
echo "🚀 AI Data Platform - Complete Setup and Test"
echo "=============================================="
echo

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Please run this script from the ai-data-platform root directory"
    exit 1
fi

# Step 1: Setup environment files
echo "📝 Step 1: Setting up environment files..."
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "✅ Created backend/.env"
else
    echo "✅ backend/.env already exists"
fi

if [ ! -f frontend/.env.local ]; then
    cp frontend/.env.example frontend/.env.local
    echo "✅ Created frontend/.env.local"
else
    echo "✅ frontend/.env.local already exists"
fi

# Step 2: Create data directories
echo
echo "📁 Step 2: Creating data directories..."
mkdir -p data/uploads data/raw data/processed data/lake data/models data/cache/huggingface logs
echo "✅ Data directories created"

# Step 3: Start services
echo
echo "🐳 Step 3: Starting Docker services..."
docker-compose down > /dev/null 2>&1  # Clean shutdown first
docker-compose up -d

# Step 4: Wait for services to be ready
echo
echo "⏳ Step 4: Waiting for services to start..."
echo "This may take a few minutes on first run..."

# Wait for PostgreSQL
echo -n "Waiting for PostgreSQL... "
for i in {1..30}; do
    if docker exec ai-platform-postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ Ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Timeout"
        exit 1
    fi
    sleep 2
done

# Wait for Redis
echo -n "Waiting for Redis... "
for i in {1..30}; do
    if docker exec ai-platform-redis redis-cli ping > /dev/null 2>&1; then
        echo "✅ Ready"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Timeout"
        exit 1
    fi
    sleep 2
done

# Wait for Backend
echo -n "Waiting for Backend API... "
for i in {1..60}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Ready"
        break
    fi
    if [ $i -eq 60 ]; then
        echo "❌ Timeout"
        exit 1
    fi
    sleep 3
done

# Wait for Frontend
echo -n "Waiting for Frontend... "
for i in {1..60}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Ready"
        break
    fi
    if [ $i -eq 60 ]; then
        echo "❌ Timeout"
        exit 1
    fi
    sleep 3
done

# Step 5: Run integration tests
echo
echo "🧪 Step 5: Running integration tests..."
./scripts/test-platform.sh

# Check test results
if [ $? -eq 0 ]; then
    echo
    echo "🎉 SUCCESS! AI Data Platform is fully operational!"
    echo
    echo "🌐 Access your platform:"
    echo "   Frontend:    http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Docs:    http://localhost:8000/docs"
    echo "   Jupyter:     http://localhost:8888 (token: ai-platform-token)"
    echo
    echo "👤 Test user created:"
    echo "   Email:    <EMAIL>"
    echo "   Password: testpassword123"
    echo
    echo "📚 Next steps:"
    echo "   1. Visit http://localhost:3000 and log in"
    echo "   2. Create your first project"
    echo "   3. Upload some data or connect a data source"
    echo "   4. Build a workflow using the visual builder"
    echo "   5. Train an ML model"
    echo
    echo "🛑 To stop the platform: docker-compose down"
    echo "🔄 To restart: docker-compose up -d"
    echo
else
    echo
    echo "❌ Setup completed but some tests failed."
    echo "The platform may still be usable, but please check the logs."
    echo
    echo "🔧 Troubleshooting:"
    echo "   docker-compose logs [service-name]"
    echo "   docker-compose ps"
    echo
fi
