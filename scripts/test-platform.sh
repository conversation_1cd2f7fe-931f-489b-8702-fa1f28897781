#!/bin/bash

# AI Data Platform Integration Test Script
echo "🧪 Testing AI Data Platform Integration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -n "Testing $test_name... "
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ PASSED${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗ FAILED${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local endpoint="$1"
    local expected_status="$2"
    
    local status=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint")
    [ "$status" = "$expected_status" ]
}

# Function to test service availability
test_service() {
    local service="$1"
    local port="$2"
    
    nc -z localhost "$port"
}

echo "🔍 Running integration tests..."
echo

# Test 1: Check if Docker services are running
echo "📦 Testing Docker Services..."
run_test "PostgreSQL service" "test_service localhost 5432"
run_test "Redis service" "test_service localhost 6379"
run_test "Backend service" "test_service localhost 8000"
run_test "Frontend service" "test_service localhost 3000"

echo

# Test 2: Test API endpoints
echo "🌐 Testing API Endpoints..."
run_test "Backend health check" "test_endpoint http://localhost:8000/health 200"
run_test "API root endpoint" "test_endpoint http://localhost:8000/ 200"
run_test "API documentation" "test_endpoint http://localhost:8000/docs 200"
run_test "Frontend homepage" "test_endpoint http://localhost:3000 200"

echo

# Test 3: Test API functionality
echo "🔧 Testing API Functionality..."

# Test user registration
echo -n "Testing user registration... "
REGISTER_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "testpassword123",
    "full_name": "Test User"
  }')

if echo "$REGISTER_RESPONSE" | grep -q '"id"'; then
    echo -e "${GREEN}✓ PASSED${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗ FAILED${NC}"
    ((TESTS_FAILED++))
fi

# Test user login
echo -n "Testing user login... "
LOGIN_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }')

if echo "$LOGIN_RESPONSE" | grep -q '"access_token"'; then
    echo -e "${GREEN}✓ PASSED${NC}"
    ((TESTS_PASSED++))
    
    # Extract access token for further tests
    ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
else
    echo -e "${RED}✗ FAILED${NC}"
    ((TESTS_FAILED++))
    ACCESS_TOKEN=""
fi

# Test authenticated endpoints if we have a token
if [ -n "$ACCESS_TOKEN" ]; then
    echo -n "Testing authenticated project creation... "
    PROJECT_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/projects" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -d '{
        "name": "Test Project",
        "description": "A test project for integration testing",
        "type": "data_science"
      }')
    
    if echo "$PROJECT_RESPONSE" | grep -q '"id"'; then
        echo -e "${GREEN}✓ PASSED${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAILED${NC}"
        ((TESTS_FAILED++))
    fi
    
    echo -n "Testing project listing... "
    PROJECTS_RESPONSE=$(curl -s -X GET "http://localhost:8000/api/v1/projects" \
      -H "Authorization: Bearer $ACCESS_TOKEN")
    
    if echo "$PROJECTS_RESPONSE" | grep -q '"projects"'; then
        echo -e "${GREEN}✓ PASSED${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAILED${NC}"
        ((TESTS_FAILED++))
    fi
fi

echo

# Test 4: Test database connectivity
echo "🗄️  Testing Database Connectivity..."
echo -n "Testing PostgreSQL connection... "
if docker exec ai-platform-postgres psql -U postgres -d ai_platform -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ PASSED${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗ FAILED${NC}"
    ((TESTS_FAILED++))
fi

echo -n "Testing Redis connection... "
if docker exec ai-platform-redis redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}✓ PASSED${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗ FAILED${NC}"
    ((TESTS_FAILED++))
fi

echo

# Test 5: Test file system
echo "📁 Testing File System..."
run_test "Data directories exist" "[ -d data/uploads ] && [ -d data/models ] && [ -d data/processed ]"
run_test "Backend can write to data directory" "touch data/test_file && rm data/test_file"

echo

# Test 6: Test Celery workers
echo "⚙️  Testing Background Workers..."
echo -n "Testing Celery worker status... "
if docker exec ai-platform-celery-worker celery -A app.workers.celery_app inspect ping > /dev/null 2>&1; then
    echo -e "${GREEN}✓ PASSED${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${YELLOW}⚠ SKIPPED${NC} (Worker may not be fully started)"
fi

echo

# Test 7: Test frontend functionality
echo "⚛️  Testing Frontend..."
echo -n "Testing frontend build... "
if curl -s http://localhost:3000 | grep -q "AI Data Platform"; then
    echo -e "${GREEN}✓ PASSED${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗ FAILED${NC}"
    ((TESTS_FAILED++))
fi

echo

# Summary
echo "📊 Test Results Summary"
echo "======================"
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo
    echo -e "${GREEN}🎉 All tests passed! The AI Data Platform is working correctly.${NC}"
    echo
    echo "🚀 You can now access:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend API: http://localhost:8000"
    echo "   API Docs: http://localhost:8000/docs"
    echo "   Jupyter Lab: http://localhost:8888 (token: ai-platform-token)"
    echo
    echo "👤 Test user credentials:"
    echo "   Email: <EMAIL>"
    echo "   Password: testpassword123"
    exit 0
else
    echo
    echo -e "${RED}❌ Some tests failed. Please check the logs and fix the issues.${NC}"
    echo
    echo "🔧 Troubleshooting tips:"
    echo "   1. Make sure all services are running: docker-compose ps"
    echo "   2. Check service logs: docker-compose logs [service-name]"
    echo "   3. Restart services: docker-compose restart"
    echo "   4. Check environment configuration in .env files"
    exit 1
fi
