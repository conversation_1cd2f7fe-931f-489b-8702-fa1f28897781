import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 20 }, // Ramp up to 20 users
    { duration: '5m', target: 20 }, // Stay at 20 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.05'],    // Error rate must be below 5%
    errors: ['rate<0.05'],             // Custom error rate below 5%
  },
};

// Base URL
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';

// Test data
const testUser = {
  email: '<EMAIL>',
  username: 'loadtestuser',
  password: 'LoadTest123!',
  full_name: 'Load Test User'
};

// Setup function - runs once per VU
export function setup() {
  // Register test user
  const registerResponse = http.post(`${BASE_URL}/api/v1/auth/register`, JSON.stringify(testUser), {
    headers: { 'Content-Type': 'application/json' },
  });
  
  if (registerResponse.status === 201 || registerResponse.status === 400) {
    // User created or already exists
    console.log('Test user setup completed');
  }
  
  return { testUser };
}

// Main test function
export default function(data) {
  // Test 1: Health Check
  testHealthCheck();
  
  // Test 2: Authentication Flow
  const authToken = testAuthentication(data.testUser);
  
  if (authToken) {
    // Test 3: Project Operations
    testProjectOperations(authToken);
    
    // Test 4: Data Operations
    testDataOperations(authToken);
    
    // Test 5: ML Operations
    testMLOperations(authToken);
  }
  
  sleep(1);
}

function testHealthCheck() {
  const response = http.get(`${BASE_URL}/health`);
  
  check(response, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 500ms': (r) => r.timings.duration < 500,
  });
  
  errorRate.add(response.status !== 200);
  responseTime.add(response.timings.duration);
}

function testAuthentication(user) {
  // Login
  const loginResponse = http.post(
    `${BASE_URL}/api/v1/auth/login`,
    JSON.stringify({
      email: user.email,
      password: user.password
    }),
    {
      headers: { 'Content-Type': 'application/json' },
    }
  );
  
  const loginSuccess = check(loginResponse, {
    'login status is 200': (r) => r.status === 200,
    'login response time < 1000ms': (r) => r.timings.duration < 1000,
    'login returns access token': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.access_token !== undefined;
      } catch (e) {
        return false;
      }
    },
  });
  
  errorRate.add(!loginSuccess);
  responseTime.add(loginResponse.timings.duration);
  
  if (loginSuccess) {
    const body = JSON.parse(loginResponse.body);
    return body.access_token;
  }
  
  return null;
}

function testProjectOperations(authToken) {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`,
  };
  
  // List projects
  const listResponse = http.get(`${BASE_URL}/api/v1/projects`, { headers });
  
  check(listResponse, {
    'list projects status is 200': (r) => r.status === 200,
    'list projects response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  errorRate.add(listResponse.status !== 200);
  responseTime.add(listResponse.timings.duration);
  
  // Create project
  const createResponse = http.post(
    `${BASE_URL}/api/v1/projects`,
    JSON.stringify({
      name: `Load Test Project ${Date.now()}`,
      description: 'Project created during load testing',
      type: 'data_science'
    }),
    { headers }
  );
  
  check(createResponse, {
    'create project status is 201': (r) => r.status === 201,
    'create project response time < 2000ms': (r) => r.timings.duration < 2000,
  });
  
  errorRate.add(createResponse.status !== 201);
  responseTime.add(createResponse.timings.duration);
  
  // Get project details if created successfully
  if (createResponse.status === 201) {
    const project = JSON.parse(createResponse.body);
    const getResponse = http.get(`${BASE_URL}/api/v1/projects/${project.id}`, { headers });
    
    check(getResponse, {
      'get project status is 200': (r) => r.status === 200,
      'get project response time < 1000ms': (r) => r.timings.duration < 1000,
    });
    
    errorRate.add(getResponse.status !== 200);
    responseTime.add(getResponse.timings.duration);
  }
}

function testDataOperations(authToken) {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`,
  };
  
  // List data sources
  const listResponse = http.get(`${BASE_URL}/api/v1/data/sources`, { headers });
  
  check(listResponse, {
    'list data sources status is 200': (r) => r.status === 200,
    'list data sources response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  errorRate.add(listResponse.status !== 200);
  responseTime.add(listResponse.timings.duration);
  
  // Test data analysis endpoint
  const analysisResponse = http.get(`${BASE_URL}/api/v1/data/analysis/sample`, { headers });
  
  check(analysisResponse, {
    'data analysis response time < 3000ms': (r) => r.timings.duration < 3000,
  });
  
  responseTime.add(analysisResponse.timings.duration);
}

function testMLOperations(authToken) {
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`,
  };
  
  // List ML models
  const listResponse = http.get(`${BASE_URL}/api/v1/ml/models`, { headers });
  
  check(listResponse, {
    'list ML models status is 200': (r) => r.status === 200,
    'list ML models response time < 1000ms': (r) => r.timings.duration < 1000,
  });
  
  errorRate.add(listResponse.status !== 200);
  responseTime.add(listResponse.timings.duration);
  
  // Test model prediction endpoint (if models exist)
  const modelsBody = JSON.parse(listResponse.body);
  if (modelsBody.models && modelsBody.models.length > 0) {
    const modelId = modelsBody.models[0].id;
    const predictionResponse = http.post(
      `${BASE_URL}/api/v1/ml/models/${modelId}/predict`,
      JSON.stringify({
        features: [1, 2, 3, 4, 5]
      }),
      { headers }
    );
    
    check(predictionResponse, {
      'model prediction response time < 5000ms': (r) => r.timings.duration < 5000,
    });
    
    responseTime.add(predictionResponse.timings.duration);
  }
}

// Teardown function - runs once after all VUs finish
export function teardown(data) {
  console.log('Load test completed');
}
